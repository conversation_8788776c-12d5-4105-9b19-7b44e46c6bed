package com.tianmasys.ebu.qingma.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ThinkModelFunctionMapper;
import com.tianmasys.ebu.qingma.service.IThinkModelFunctionService;
import com.tianmasys.ebu.qingma.vo.ThinkModelFunctionVO;
import com.tianmasys.ebu.qingma.domain.ThinkModelFunctionEntity;

/**
 * iot产品物模型功能Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */

@Service
public class ThinkModelFunctionServiceImpl extends ServiceImpl<ThinkModelFunctionMapper,ThinkModelFunctionEntity> implements IThinkModelFunctionService 
{
    @Autowired
    private ThinkModelFunctionMapper thinkModelFunctionMapper;

    /**
     * 查询iot产品物模型功能详情
     * 
     * @param id iot产品物模型功能主键
     * @return iot产品物模型功能
     */
    @Override
    public ThinkModelFunctionVO selectThinkModelFunctionById(Long id)
    {
        ThinkModelFunctionEntity entity = thinkModelFunctionMapper.selectById(id);
        return BeanUtil.toBean(entity,ThinkModelFunctionVO.class);
    }

    /**
     * 查询iot产品物模型功能列表
     * 
     * @param thinkModelFunctionVO iot产品物模型功能
     * @return iot产品物模型功能
     */
    @Override
    public List<ThinkModelFunctionVO> selectThinkModelFunctionList(ThinkModelFunctionVO thinkModelFunctionVO)
    {
        List<ThinkModelFunctionEntity> thinkModelFunctionEntities = thinkModelFunctionMapper.selectList(getWrapper(thinkModelFunctionVO));
        return PageObjectConvertUtil.convert(thinkModelFunctionEntities,ThinkModelFunctionVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<ThinkModelFunctionEntity> getWrapper(ThinkModelFunctionVO query){
        LambdaQueryWrapper<ThinkModelFunctionEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getIdentifier()), ThinkModelFunctionEntity::getIdentifier, query.getIdentifier());
        wrapper.like(ObjUtil.isNotEmpty(query.getName()), ThinkModelFunctionEntity::getName, query.getName());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDescription()), ThinkModelFunctionEntity::getDescription, query.getDescription());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProductId()), ThinkModelFunctionEntity::getProductId, query.getProductId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProductKey()), ThinkModelFunctionEntity::getProductKey, query.getProductKey());
        wrapper.eq(ObjUtil.isNotEmpty(query.getType()), ThinkModelFunctionEntity::getType, query.getType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProperty()), ThinkModelFunctionEntity::getProperty, query.getProperty());
        wrapper.eq(ObjUtil.isNotEmpty(query.getEvent()), ThinkModelFunctionEntity::getEvent, query.getEvent());
        wrapper.eq(ObjUtil.isNotEmpty(query.getService()), ThinkModelFunctionEntity::getService, query.getService());
        return wrapper;
    }

    /**
     * 新增iot产品物模型功能
     * 
     * @param thinkModelFunctionVO iot产品物模型功能
     * @return 结果
     */
    @Override
    public int insertThinkModelFunction(ThinkModelFunctionVO thinkModelFunctionVO)
    {
       ThinkModelFunctionEntity entity = BeanUtil.toBean(thinkModelFunctionVO, ThinkModelFunctionEntity.class);
		 return thinkModelFunctionMapper.insert(entity);
    }

    /**
     * 修改iot产品物模型功能
     * 
     * @param thinkModelFunctionVO iot产品物模型功能
     * @return 结果
     */
    @Override
    public int updateThinkModelFunction(ThinkModelFunctionVO thinkModelFunctionVO)
    {
       ThinkModelFunctionEntity entity = BeanUtil.toBean(thinkModelFunctionVO, ThinkModelFunctionEntity.class);
        return thinkModelFunctionMapper.updateById(entity);
    }

    /**
     * 批量删除iot产品物模型功能
     * 
     * @param ids 需要删除的iot产品物模型功能主键
     * @return 结果
     */
    @Override
    public int deleteThinkModelFunctionByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return thinkModelFunctionMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除iot产品物模型功能信息
     * 
     * @param id iot产品物模型功能主键
     * @return 结果
     */
    @Override
    public int deleteThinkModelFunctionById(Long id)
    {

        return thinkModelFunctionMapper.deleteById(id);
    }

}
