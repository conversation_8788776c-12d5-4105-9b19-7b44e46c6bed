package com.tianmasys.ebu.qingma.domain;

import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;

/**
 * modbus点位
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */

@Data
@TableName("modbus_points")
public class ModbusPointsEntity extends BaseEntity {
	/**
	* 点位 ID
	*/
	@TableId("id")
	private Long id;

	/**
	* 点位名称
	*/
    @TableField("point_name")
	private String pointName;

	/**
	* 点位描述
	*/
    @TableField("description")
	private String description;

	/**
	* 所属站点ID
	*/
    @TableField("site_id")
	private Long siteId;

	/**
	* 主站ID
	*/
    @TableField("master_id")
	private Long masterId;

	/**
	* 从站ID
	*/
    @TableField("slave_id")
	private Long slaveId;

	/**
	* 功能码：1-COIL_STATUS;2-INPUT_STATUS;3-HOLDING_REGISTER;4-INPUT_REGISTER
	*/
    @TableField("function_code")
	private String functionCode;

	/**
	* 寄存器地址
	*/
    @TableField("register_address")
	private Integer registerAddress;

	/**
	* 状态：0-禁用；1-启用;
	*/
    @TableField("`status`")
	private Integer status;

	/**
	* 自动读取：0-不读取；1-自动读取;
	*/
    @TableField("auto_read")
	private Boolean autoRead;

	/**
	* 采集间隔，单位毫秒
	*/
    @TableField("`interval`")
	private Integer interval;

	/**
	* 数据类型:2-TWO_BYTE_INT_UNSIGNED;3-TWO_BYTE_INT_SIGNED;
	*/
    @TableField("data_type")
	private String dataType;

	/**
	* 映射键名称，json。支持对象和path
	*/
    @TableField("mapping_config")
	private String mappingConfig;

	/** 是否删除：0-正常；1-删除； */
	@TableField(fill = FieldFill.INSERT)
	@TableLogic
	private String deleteFlag;

}