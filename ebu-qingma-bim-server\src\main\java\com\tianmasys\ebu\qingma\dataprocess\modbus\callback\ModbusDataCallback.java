package com.tianmasys.ebu.qingma.dataprocess.modbus.callback;

import com.tianmasys.ebu.qingma.dataprocess.modbus.config.MasterData;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.SlaveData;

import java.util.Map;

/**
 * Modbus数据处理回调接口
 */
public interface ModbusDataCallback {

    /**
     * 数据处理回调方法
     * @param slaveData 从站数据
     * @param data 读取到的数据
     */
    void onDataReceived(MasterData masterData, SlaveData slaveData, Map<String, Object> data);
}
