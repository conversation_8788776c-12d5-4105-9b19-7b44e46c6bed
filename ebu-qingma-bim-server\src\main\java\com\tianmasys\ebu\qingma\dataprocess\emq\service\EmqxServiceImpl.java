package com.tianmasys.ebu.qingma.dataprocess.emq.service;

import cn.hutool.core.util.ReUtil;
import com.alibaba.fastjson2.JSON;
import com.tianmasys.ebu.qingma.dataprocess.emq.config.MqttConfig;
import com.tianmasys.ebu.qingma.dataprocess.emq.service.cmd.CmdProcess;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 用于处理MQTT消息的具体业务逻辑，如订阅回调
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnBean(MqttConfig.class)
public class EmqxServiceImpl implements EmqxService {

    @Resource
    private MqttConfig mqttConfig;

    @Resource
    private CmdProcess cmdProcessImpl;

    // TODO 多线程处理消息
    @Override
    public void subscribeCallback(String topic, MqttMessage mqttMessage) {
        log.info("收到消息，主题: {}, 内容: {}", topic, new String(mqttMessage.getPayload()));
        // 根据不同的主题，处理不同的业务逻辑
        if (topic.contains(mqttConfig.getDefaultTopic())) {
            // 设备上报数据
            String proId = ReUtil.getGroup1("/pub/(.*)",topic);

            cmdProcessImpl.cmdProcess(proId, JSON.parseObject(new String(mqttMessage.getPayload())));

        }
    }

    @Override
    public void subscribe(MqttClient client) {
        try {
            // 订阅默认主题，可以根据需要修改
//            client.subscribe("$share/yudao/+/+/#", 1);
            client.subscribe(mqttConfig.getDefaultTopic());
            log.info("订阅默认主题成功");
        } catch (Exception e) {
            log.error("订阅默认主题失败", e);
        }
    }
}
