package com.tianmasys.ebu.qingma.config.properties;

//import com.tianmasys.ebu.qingma.config.RouteConfig;
//import com.tianmasys.ebu.qingma.config.feign.FeignRouteConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

//import java.util.List;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "biz")
@Setter
@Getter
public class FeignRouteConfigProperties {


//    private  List<FeignRouteConfig> feignRoutes;
    private Map<String,String> feignRoutes;

}