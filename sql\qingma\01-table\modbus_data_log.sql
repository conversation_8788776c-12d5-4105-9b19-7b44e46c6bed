-- ------------------------------------
--  表名称:modbus点位数据记录表
--  适用数据库：MySql
--  表名称：modbus_data_log
--  字段前缀 ：无
--  最后修改人：<EMAIL>
--  最后修改日期：2025.08.05
-- ------------------------------------

DROP TABLE IF EXISTS modbus_data_log;
CREATE TABLE modbus_data_log (
  id                           bigint UNSIGNED  NOT NULL AUTO_INCREMENT  comment '主键ID',
  site_id                      bigint        NOT NULL            comment '站点id',
  slave_id                     bigint        DEFAULT NULL        comment '从站id',
  start_time                   datetime      DEFAULT NULL        comment '采集开始时间',
  fnish_time                   datetime      DEFAULT NULL        comment '采集完成时间',
  time                         int           DEFAULT NULL        comment '采集耗时（单位毫秒）',
  data                         json          DEFAULT NULL        comment '采集的数据',
  delete_flag                  varchar(2)    DEFAULT NULL        comment '是否删除：0-正常；1-删除；',
  create_time                  datetime      DEFAULT NULL        comment '创建时间',
  create_by                    varchar(64)   DEFAULT NULL        comment '创建用户id',
  update_time                  datetime      DEFAULT NULL        comment '修改时间',
  update_by                    varchar(64)   DEFAULT NULL        comment '修改用户id',
  tenant_id                    bigint        NOT NULL DEFAULT 0  comment '租户编号',
  primary key (id)
) comment 'modbus点位数据记录表';
