package com.tianmasys.ebu.qingma.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.tianmasys.ebu.qingma.vo.DeviceCmdVo;
import com.tianmasys.ebu.qingma.vo.DeviceVO;
import com.tianmasys.ebu.qingma.domain.DeviceEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tianmasys.ebu.qingma.vo.EmqxClientReqVo;
import com.tianmasys.ebu.qingma.vo.EmqxClientRespVo;


/**
 * iot设备表Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */
public interface IDeviceService extends IService<DeviceEntity>
{
    /**
     * 查询device
     * 
     * @param id device主键
     * @return device
     */
    public DeviceVO selectDeviceById(Long id);

    /**
     * 查询device列表
     * 
     * @param deviceVO device
     * @return device集合
     */
    public List<DeviceVO> selectDeviceList(DeviceVO deviceVO);

    /**
     * 新增device
     * 
     * @param deviceVO device
     * @return 结果
     */
    public int insertDevice(DeviceVO deviceVO);

    /**
     * 修改device
     * 
     * @param deviceVO device
     * @return 结果
     */
    public int updateDevice(DeviceVO deviceVO);

    /**
     * 批量删除device
     * 
     * @param ids 需要删除的device主键集合
     * @return 结果
     */
    public int deleteDeviceByIds(Long[] ids);

    /**
     * 删除device信息
     * 
     * @param id device主键
     * @return 结果
     */
    public int deleteDeviceById(Long id);


    /**
     * 设备状态更新
     *
     * @param proId 产品ID
     * @param devId 设备id
     * @return 结果
     */
    public void deviceUpdateStatus(String proId,String devId,JSONObject msg);

    /**
     * mqtt 服务端事件处理
     *
     * @param topic 主题
     * @param eventJson 事件内容
     * @return 结果
     */
    public void dealMqttEvent(String topic, JSONObject eventJson);

    /**
     * 下发指令到设备
     *
     * @param deviceCmdVo 命令参数
     * @return 结果
     */
    public JSONObject deviceCmd(DeviceCmdVo deviceCmdVo);


    /**
     * 查询emqx客户端列表
     */
    public EmqxClientRespVo qryEmqxClientList(EmqxClientReqVo qmqxClientReqVo);
}
