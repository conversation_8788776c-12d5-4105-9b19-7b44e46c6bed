-- ------------------------------------
--  表名称:iot产品物模型功能
--  适用数据库：MySql
--  表名称：iot_think_model_function
--  字段前缀 ：无
--  最后修改人：<EMAIL>
--  最后修改日期：2025.01.07
-- ------------------------------------

DROP TABLE IF EXISTS iot_think_model_function;
CREATE TABLE iot_think_model_function (
  id                           bigint NOT NULL AUTO_INCREMENT    comment '设备 ID',
  identifier                   varchar(32)   NOT NULL            comment '设备唯一标识符(全局唯一，用于识别设备)',
  name                         varchar(64)   NOT NULL            comment '功能名称',
  description                  varchar(64)   NOT NULL            comment '功能描述',
  product_id                   bigint        DEFAULT NULL        comment '产品id',
  product_key                  varchar(64)   DEFAULT NULL        comment '产品标识',
  type                         varchar(2)    DEFAULT NULL        comment '功能类型:0-属性;1-服务;2-事件;',
  property                     varchar(2048)    DEFAULT NULL        comment '属性',
  event                        varchar(2048)    DEFAULT NULL        comment '事件',
  service                      varchar(2048)    DEFAULT NULL        comment '服务',
  delete_flag                  varchar(2)    DEFAULT NULL        comment '是否删除：0-正常；1-删除；',
  create_time                  datetime      DEFAULT NULL        comment '创建时间',
  create_by                    varchar(64)   DEFAULT NULL        comment '创建用户id',
  update_time                  datetime      DEFAULT NULL        comment '修改时间',
  update_by                    varchar(64)   DEFAULT NULL        comment '修改用户id',
  tenant_id                    bigint        NOT NULL DEFAULT 0  comment '租户编号',
  primary key (id)
) comment 'iot产品物模型功能';
