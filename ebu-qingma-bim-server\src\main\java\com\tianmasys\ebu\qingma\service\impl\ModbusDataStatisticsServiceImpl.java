package com.tianmasys.ebu.qingma.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.ModbusDataStatisticsMapper;
import com.tianmasys.ebu.qingma.service.IModbusDataStatisticsService;
import com.tianmasys.ebu.qingma.vo.ModbusDataStatisticsVO;
import com.tianmasys.ebu.qingma.domain.ModbusDataStatisticsEntity;

/**
 * modbus统计数据统计表Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-07
 */

@Service
public class ModbusDataStatisticsServiceImpl extends ServiceImpl<ModbusDataStatisticsMapper,ModbusDataStatisticsEntity> implements IModbusDataStatisticsService 
{
    @Autowired
    private ModbusDataStatisticsMapper modbusDataStatisticsMapper;

    /**
     * 查询modbus统计数据统计表详情
     * 
     * @param id modbus统计数据统计表主键
     * @return modbus统计数据统计表
     */
    @Override
    public ModbusDataStatisticsVO selectModbusDataStatisticsById(Long id)
    {
        ModbusDataStatisticsEntity entity = modbusDataStatisticsMapper.selectById(id);
        return BeanUtil.toBean(entity,ModbusDataStatisticsVO.class);
    }

    /**
     * 查询modbus统计数据统计表列表
     * 
     * @param modbusDataStatisticsVO modbus统计数据统计表
     * @return modbus统计数据统计表
     */
    @Override
    public List<ModbusDataStatisticsVO> selectModbusDataStatisticsList(ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
        List<ModbusDataStatisticsEntity> modbusDataStatisticsEntities = modbusDataStatisticsMapper.selectList(getWrapper(modbusDataStatisticsVO));
        return PageObjectConvertUtil.convert(modbusDataStatisticsEntities,ModbusDataStatisticsVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<ModbusDataStatisticsEntity> getWrapper(ModbusDataStatisticsVO query){
        LambdaQueryWrapper<ModbusDataStatisticsEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getSiteId()), ModbusDataStatisticsEntity::getSiteId, query.getSiteId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getStatType()), ModbusDataStatisticsEntity::getStatType, query.getStatType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getStartTime()), ModbusDataStatisticsEntity::getStartTime, query.getStartTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getEndTime()), ModbusDataStatisticsEntity::getEndTime, query.getEndTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getData()), ModbusDataStatisticsEntity::getData, query.getData());
        return wrapper;
    }

    /**
     * 新增modbus统计数据统计表
     * 
     * @param modbusDataStatisticsVO modbus统计数据统计表
     * @return 结果
     */
    @Override
    public int insertModbusDataStatistics(ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
       ModbusDataStatisticsEntity entity = BeanUtil.toBean(modbusDataStatisticsVO, ModbusDataStatisticsEntity.class);
       return modbusDataStatisticsMapper.insert(entity);
    }

    /**
     * 修改modbus统计数据统计表
     * 
     * @param modbusDataStatisticsVO modbus统计数据统计表
     * @return 结果
     */
    @Override
    public int updateModbusDataStatistics(ModbusDataStatisticsVO modbusDataStatisticsVO)
    {
       ModbusDataStatisticsEntity entity = BeanUtil.toBean(modbusDataStatisticsVO, ModbusDataStatisticsEntity.class);
        return modbusDataStatisticsMapper.updateById(entity);
    }

    /**
     * 批量删除modbus统计数据统计表
     * 
     * @param ids 需要删除的modbus统计数据统计表主键
     * @return 结果
     */
    @Override
    public int deleteModbusDataStatisticsByIds(Long[] ids)
    {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        return modbusDataStatisticsMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除modbus统计数据统计表信息
     * 
     * @param id modbus统计数据统计表主键
     * @return 结果
     */
    @Override
    public int deleteModbusDataStatisticsById(Long id)
    {

        return modbusDataStatisticsMapper.deleteById(id);
    }

}
