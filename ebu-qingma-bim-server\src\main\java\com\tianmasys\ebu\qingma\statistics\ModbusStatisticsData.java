package com.tianmasys.ebu.qingma.statistics;

import cn.hutool.core.util.NumberUtil;
import lombok.Data;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Modbus统计数据类
 * 用于在内存中存储实时统计数据
 */
@Data
public class ModbusStatisticsData {
    // 按站点ID存储统计数据
    private Map<Long, SiteStatisticsData> siteDataMap = new ConcurrentHashMap<>();

    /**
     * 站点统计数据
     */
    @Data
    public static class SiteStatisticsData {
        private Long siteId;
        // 按统计类型存储数据（minute, hour, day）
        private Map<String, StatisticTypeData> typeDataMap = new ConcurrentHashMap<>();
    }

    /**
     * 按统计类型存储的数据
     */
    @Data
    public static class StatisticTypeData {
        private String statType; // minute, hour, day
        private Date startTime; // 当前统计周期开始时间
        private Date endTime; // 当前统计周期结束时间
        // 存储每个数据点的累计值和计数
        private Map<String, DataPointStats> dataPoints = new ConcurrentHashMap<>();
        // 存储上一次计算的平均值，用于计算趋势
        private Map<String, Double> previousAverages = new ConcurrentHashMap<>();
    }

    /**
     * 数据点统计信息
     */
    @Data
    public static class DataPointStats {
        private String key; // 数据点名称，如 temperature, humidity 等
        private double sum; // 累计值
        private int count; // 数据点数量
        private Object lastValue; // 最新值
        private Object unit; // 单位
        private Object status; // 状态
        private Object trend; // 趋势

        /**
         * 添加新值
         * @param value 新值
         */
        public void addValue(Object value) {
            if (value instanceof Number) {
                this.sum += ((Number) value).doubleValue();
            }
            this.count++;
            this.lastValue = value;
        }

        /**
         * 计算平均值
         * @return 平均值
         */
        public double getAverage() {
            if (count == 0) {
                return 0;
            }
            return sum / count;
        }

        /**
         * 重置统计数据
         */
        public void reset() {
            this.sum = 0;
            this.count = 0;
        }
    }
}