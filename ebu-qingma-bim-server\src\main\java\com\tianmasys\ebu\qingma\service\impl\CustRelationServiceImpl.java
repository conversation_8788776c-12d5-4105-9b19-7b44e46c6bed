package com.tianmasys.ebu.qingma.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tianmasys.ebu.common.core.constant.SecurityConstants;
import com.tianmasys.ebu.common.core.domain.R;
import com.tianmasys.ebu.common.core.exception.BusinessException;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.system.api.RemoteCustFeignService;
import com.tianmasys.ebu.system.api.domain.CustAuthReqDto;
import com.tianmasys.ebu.system.api.domain.cust.CustAuthDto;
import com.tianmasys.ebu.system.api.domain.cust.CustBaseInfoDto;
import com.tianmasys.ebu.qingma.service.CommonCustService;
import com.tianmasys.ebu.qingma.vo.SubCustVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.CustRelationMapper;
import com.tianmasys.ebu.qingma.service.ICustRelationService;
import com.tianmasys.ebu.qingma.vo.CustRelationVO;
import com.tianmasys.ebu.qingma.domain.CustRelationEntity;

/**
 * 客户关联关系Service业务层处理
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-02-21
 */

@Service
@Slf4j
public class CustRelationServiceImpl extends ServiceImpl<CustRelationMapper,CustRelationEntity> implements ICustRelationService 
{
    @Autowired
    private CustRelationMapper custRelationMapper;


    @Autowired
    private RemoteCustFeignService remoteCustFeignService;

    @Autowired
    private CommonCustService commonCustService;



    /**
     * 查询客户关联关系详情
     * 
     * @param custId 客户关联关系主键
     * @return 客户关联关系
     */
    @Override
    public CustRelationVO selectCustRelationByCustId(String custId)
    {
        CustRelationEntity entity = custRelationMapper.selectCustRelationByCustId(custId);
        return BeanUtil.toBean(entity,CustRelationVO.class);
    }

    /**
     * 查询客户关联关系列表
     * 
     * @param custRelationVO 客户关联关系
     * @return 客户关联关系
     */
    @Override
    public List<CustRelationVO> selectCustRelationList(CustRelationVO custRelationVO)
    {
//        List<CustRelationEntity> custRelationEntities = custRelationMapper.selectList(getWrapper(custRelationVO));
        List<String> custIdList = new ArrayList<>();
        if(StringUtils.isNotBlank(custRelationVO.getCustId())){
             custIdList = commonCustService.selectSubCustIdsByCustId(custRelationVO.getCustId());
        }
        List<CustRelationEntity> custRelationEntities = custRelationMapper.selectCustRelationList(BeanUtil.toBean(custRelationVO,CustRelationEntity.class),custIdList);
        return PageObjectConvertUtil.convert(custRelationEntities,CustRelationVO.class);
    }

  
     /**
     * 构造查询器
     * 
     */
    private LambdaQueryWrapper<CustRelationEntity> getWrapper(CustRelationVO query){
        LambdaQueryWrapper<CustRelationEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getParentCustId()), CustRelationEntity::getParentCustId, query.getParentCustId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getAppId()), CustRelationEntity::getAppId, query.getAppId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getCustLeave()), CustRelationEntity::getCustLeave, query.getCustLeave());
        return wrapper;
    }

    /**
     * 新增客户关联关系
     * 
     * @param custRelationVO 客户关联关系
     * @return 结果
     */
    @Override
    public int insertCustRelation(CustRelationVO custRelationVO)
    {
       CustRelationEntity entity = BeanUtil.toBean(custRelationVO, CustRelationEntity.class);
       return custRelationMapper.insert(entity);
    }

    /**
     * 修改客户关联关系
     * 
     * @param custRelationVO 客户关联关系
     * @return 结果
     */
    @Override
    public int updateCustRelation(CustRelationVO custRelationVO)
    {
       CustRelationEntity entity = BeanUtil.toBean(custRelationVO, CustRelationEntity.class);
        CustBaseInfoDto dto = new CustBaseInfoDto();
        dto.setCustId(entity.getCustId());
        dto.setNickName(entity.getNickName());
        dto.setMobile(entity.getMobile());
        dto.setUserName(entity.getUserName());
        dto.setAppId(custRelationVO.getAppId());
       remoteCustFeignService.updateCustInfo(dto,SecurityConstants.INNER);

        return custRelationMapper.updateById(entity);
    }

    /**
     * 批量删除客户关联关系
     * 
     * @param custIds 需要删除的客户关联关系主键
     * @return 结果
     */
    @Override
    public int deleteCustRelationByCustIds(String[] custIds)
    {
        List<String> custIdList = Arrays.stream(custIds).collect(Collectors.toList());
        return custRelationMapper.deleteByIds(custIdList);
    }

    /**
     * 单笔删除客户关联关系信息
     * 
     * @param custId 客户关联关系主键
     * @return 结果
     */
    @Override
    public int deleteCustRelationByCustId(String custId)
    {
        R<Integer> integerR = remoteCustFeignService.deleteCustAuthByCustId(custId, SecurityConstants.INNER);
        String code = integerR.getCode();
        if(!"200".equals(code)){
            throw new BusinessException(code, integerR.getMsg());
        }
        return custRelationMapper.deleteById(custId);
    }

    /**
     * 添加子账号
     *
     * @param subCustVo 子账号内容
     * @return 结果
     */
    @Override
    public int addSubCust(SubCustVo subCustVo) {
        String parentCustId = subCustVo.getParentCustId();
        Integer custLeave = subCustVo.getCustLeave();
        if(0 == custLeave.intValue()){ //添加一级
            parentCustId = "0";
        }

        CustAuthReqDto dto  = new CustAuthReqDto();
        BeanUtil.copyProperties(subCustVo,dto);
        R<CustAuthDto> custAuthDtoR = remoteCustFeignService.openCustAuth(dto, SecurityConstants.INNER);
        String code = custAuthDtoR.getCode();
        String msg = custAuthDtoR.getMsg();
        if(!StrUtil.equals("200",code)){
            log.error("添加子账号失败，请求失败，code:{},msg:{}",code,msg);
            throw  new BusinessException(msg);
        }
        CustAuthDto data = custAuthDtoR.getData();

        CustRelationVO insert = new CustRelationVO();
        insert.setCustId(data.getCustId());
        insert.setParentCustId(parentCustId);
        insert.setCustLeave(custLeave);
        insert.setAppId(subCustVo.getAppId());
        return insertCustRelation(insert);

    }

}
