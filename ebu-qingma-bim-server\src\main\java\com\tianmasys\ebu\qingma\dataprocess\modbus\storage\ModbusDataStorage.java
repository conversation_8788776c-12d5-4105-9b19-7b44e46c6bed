package com.tianmasys.ebu.qingma.dataprocess.modbus.storage;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Modbus数据存储类
 * 用于存储从各个modbus设备读取的数据，供websocket等其他组件使用
 */
@Component
public class ModbusDataStorage {

    /**
     * 存储modbus设备数据的Map
     * key: siteId
     * value: 设备数据
     */
    private final Map<String, Object> modbusDataMap = new ConcurrentHashMap<>();

    /**
     * 更新指定siteId的数据
     *
     * @param  siteId 站点ID
     * @param data    数据
     */
    public void updateData(String siteId, Object data) {
        modbusDataMap.put(siteId, data);
    }

    /**
     * 获取指定siteId的数据
     *
     * @param siteId 站点ID
     * @return 数据
     */
    public Object getData(String siteId) {
        return modbusDataMap.get(siteId);
    }

    /**
     * 获取所有数据
     *
     * @return 所有数据
     */
    public Map<String, Object> getAllData() {
        return modbusDataMap;
    }

    /**
     * 清除指定siteId的数据
     *
     * @param siteId 从站ID
     */
    public void removeData(String siteId) {
        modbusDataMap.remove(siteId);
    }
}
