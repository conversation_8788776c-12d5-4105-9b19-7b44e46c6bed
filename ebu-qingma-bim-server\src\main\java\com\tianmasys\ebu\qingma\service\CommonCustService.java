package com.tianmasys.ebu.qingma.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tianmasys.ebu.common.redis.service.RedisService;
import com.tianmasys.ebu.qingma.domain.DeviceEntity;
import com.tianmasys.ebu.qingma.mapper.CustRelationMapper;
import com.tianmasys.ebu.qingma.mapper.DeviceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 客户父子关系，获取用户子客户号
 */
@Service
public class CommonCustService {

    @Autowired
    private CustRelationMapper custRelationMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private RedisService redisService;

    private String redisKey = "custRelation:custId:";
    private String parentRedisKey = "custRelation:parentCustId:";
    private String deviceMsgRedisKey = "iot:deviceMsg:";

    /**
     * 根据custId获取下级客户id列表
     * @return 结果
     */
    public List<String> selectSubCustIdsByCustId(String custId){
        String key = getKey(custId);
        List<String> cacheList = redisService.getCacheList(key);
        if(cacheList == null || cacheList.size() == 0){
            Page<Object> localPage = PageHelper.getLocalPage();
            if(localPage != null){
                PageHelper.clearPage();
                cacheList = custRelationMapper.selectSubCustIdsByCustId(custId);
                PageHelper.startPage(localPage.getPageNum(), localPage.getPageSize(),localPage.getOrderBy());
            }else {
                cacheList = custRelationMapper.selectSubCustIdsByCustId(custId);
            }
            redisService.setCacheList(key,cacheList,30*60L, TimeUnit.SECONDS);
//            redisService.expire(key, 30*60, TimeUnit.SECONDS);
            return cacheList;
        }else {
            return cacheList;
        }
    }
    /**
     * 根据custId获取下级客户id列表
     * @return 结果
     */
    public List<String> selectParentCustIdsByCustId(String custId){
        String key = getParentKey(custId);
        List<String> cacheList = redisService.getCacheList(key);
        if(cacheList == null || cacheList.size() == 0){
            Page<Object> localPage = PageHelper.getLocalPage();
            if(localPage != null){
                PageHelper.clearPage();
                cacheList = custRelationMapper.selectParentCustIdsByCustId(custId);
                PageHelper.startPage(localPage.getPageNum(), localPage.getPageSize(),localPage.getOrderBy());
            }else {
                cacheList = custRelationMapper.selectParentCustIdsByCustId(custId);
            }
            redisService.setCacheList(key,cacheList,30*60L, TimeUnit.SECONDS);
//            redisService.expire(key, 30*60, TimeUnit.SECONDS);
            return cacheList;
        }else {
            return cacheList;
        }
    }
    /**
     * 根据custId获取子客户id列表
     * @return 结果
     */
    public List<String> selectAllDeviceId(String custId){
        List<String> custIds = selectSubCustIdsByCustId(custId);

        LambdaQueryWrapper<DeviceEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.in(DeviceEntity::getCustId,custIds);
        wrapper.select(DeviceEntity::getDeviceKey);
        List<DeviceEntity> deviceEntities = deviceMapper.selectList(wrapper);
        return deviceEntities.stream().map(DeviceEntity::getDeviceKey).collect(Collectors.toList());
    }

    /**
     * 清除缓存
     * @return
     */
    public Boolean clearSubCustIdsByCustId(String custId){
        String key = getKey(custId);
        return redisService.deleteObject(key);
    }
    /**
     * 清除所有缓存，包含父、子缓存，及消息缓存
     * @return
     */
    public void clearAllCache(){
        Collection<String> keys = redisService.keys(redisKey + "*");
        keys.stream().forEach(key->{
            redisService.deleteObject(key);
        });
        Collection<String> parentKeys = redisService.keys(parentRedisKey + "*");
        parentKeys.stream().forEach(key->{
            redisService.deleteObject(key);
        });
        Collection<String> deviceMsgKeys = redisService.keys(deviceMsgRedisKey + "*");
        deviceMsgKeys.stream().forEach(key->{
            redisService.deleteObject(key);
        });
    }

    /**
     *  发送消息缓存key，方便统一清理
     * @param deviceName
     * @param eventType
     * @param custId
     * @return
     */
    public String getMsgKey(String deviceName,String eventType,String custId){
        String msgRedisKey = String.format("%s%s_%s_%s",deviceMsgRedisKey,deviceName,eventType,custId);
        return msgRedisKey;
    }

    private String getKey(String custId){
        return  String.format("%s%s",redisKey,custId);
    }
    private String getParentKey(String custId){
        return  String.format("%s%s",parentRedisKey,custId);
    }


}
