package com.tianmasys.ebu.qingma.controller.app;

import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.utils.SecurityUtils;
import com.tianmasys.ebu.qingma.service.IDeviceEventService;
import com.tianmasys.ebu.qingma.vo.DeviceEventVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * iot设备事件表Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-01-19
 */
@RestController
@RequestMapping("/app/deviceEvent")
public class DeviceAppEventController extends BaseController
{
    @Autowired
    private IDeviceEventService deviceEventService;

    /**
     * 查询iot设备事件表列表
     */
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<DeviceEventVO>> list(DeviceEventVO deviceEventVO)
    {
        startPage();
        String custId = SecurityUtils.getCustId();
        deviceEventVO.setCustId(custId);
        List<DeviceEventVO> list = deviceEventService.selectDeviceEventList(deviceEventVO);
        return success(getDataTable(list));
    }


    /**
     * 删除iot设备事件表
     */
    @Log(title = "iot设备事件表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<DeviceEventVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceEventService.deleteDeviceEventByIds(ids));
    }
}
