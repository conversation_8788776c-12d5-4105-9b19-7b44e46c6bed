-- ------------------------------------
--  表名称:iot设备事件表
--  适用数据库：MySql
--  表名称：iot_device_event
--  字段前缀 ：无
--  最后修改人：<EMAIL>
--  最后修改日期：2025.01.07
-- ------------------------------------

DROP TABLE IF EXISTS iot_device_event;
CREATE TABLE iot_device_event (
  id                           bigint NOT NULL AUTO_INCREMENT    comment '设备 ID',
  device_id                    bigint   NOT NULL                 comment '设备id',
  device_key                   varchar(64)   NOT NULL            comment '设备唯一标识符(全局唯一，用于识别设备)',
  device_name                  varchar(64)   NOT NULL            comment '设备名称(在产品内唯一，用于标识设备)',
  nickname                     varchar(64)   NOT NULL            comment '设备备注名称',
  product_id                   bigint        DEFAULT NULL        comment '产品编号',
  product_key                  varchar(64)   DEFAULT NULL        comment '产品标识',
  device_type                  varchar(2)    DEFAULT NULL        comment '设备类型:0-直连设备;1-网关子设备;2-网关设备;',
  event_type                   varchar(2)    DEFAULT NULL        comment '事件类型:0-设备状态上报;1-注册;2-离线;3-上线;4-缺水报警;',
  event_is_push                varchar(2)    DEFAULT NULL        comment '是否推送:0-不推送;1-推送;',
  msg_template_id              bigint        DEFAULT NULL        comment '模板消息id',
  event_msg                    varchar(2048) DEFAULT NULL        comment '事件报文',
  event_time                   datetime      DEFAULT NULL        comment '事件时间',
  delete_flag                  varchar(2)    DEFAULT NULL        comment '是否删除：0-正常；1-删除；',
  create_time                  datetime      DEFAULT NULL        comment '创建时间',
  create_by                    varchar(64)   DEFAULT NULL        comment '创建用户id',
  update_time                  datetime      DEFAULT NULL        comment '修改时间',
  update_by                    varchar(64)   DEFAULT NULL        comment '修改用户id',
  tenant_id                    bigint        NOT NULL DEFAULT 0  comment '租户编号',
  primary key (id)
) comment 'iot设备事件表';
