package com.tianmasys.ebu.qingma.dataprocess.emq.start;

import com.tianmasys.ebu.qingma.dataprocess.emq.client.EmqxClient;
import com.tianmasys.ebu.qingma.dataprocess.emq.config.MqttConfig;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 用于在应用启动时自动连接MQTT服务器
 *
 * <AUTHOR>
 */
@Component
@ConditionalOnBean(MqttConfig.class)
public class EmqxStart implements ApplicationRunner {

    @Resource
    private EmqxClient emqxClient;

    @Override
    public void run(ApplicationArguments applicationArguments) {
            emqxClient.connect();
    }
}
