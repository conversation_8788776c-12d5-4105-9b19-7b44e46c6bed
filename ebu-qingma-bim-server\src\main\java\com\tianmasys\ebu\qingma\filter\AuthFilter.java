package com.tianmasys.ebu.qingma.filter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSON;
import com.tianmasys.ebu.common.core.constant.CacheConstants;
import com.tianmasys.ebu.common.core.constant.HttpStatus;
import com.tianmasys.ebu.common.core.constant.SecurityConstants;
import com.tianmasys.ebu.common.core.constant.TokenConstants;
import com.tianmasys.ebu.common.core.utils.JwtUtils;
import com.tianmasys.ebu.common.core.utils.ServletUtils;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.redis.service.RedisService;
import com.tianmasys.ebu.common.security.service.CustTokenService;
import com.tianmasys.ebu.common.security.service.TokenService;
import com.tianmasys.ebu.module.system.config.IgnoreWhiteProperties;
import com.tianmasys.ebu.system.api.domain.SysUser;
import com.tianmasys.ebu.system.api.model.LoginCust;
import com.tianmasys.ebu.system.api.model.LoginUser;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * token过滤器 验证token有效性
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class AuthFilter extends OncePerRequestFilter
{
    @Autowired
    private TokenService tokenService;
    @Autowired
    private CustTokenService custTokenService;

    // 排除过滤的 uri 地址，nacos自行添加
    @Autowired
    private IgnoreWhiteProperties ignoreWhite;

    @Autowired
    private RedisService redisService;

    @Value("${system.checkAuth:true}")
    private Boolean checkAuth;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException
    {

        String url = request.getRequestURI();
        List<String> headerList = new ArrayList<>();
//        Enumeration<String> headerNames = request.getHeaderNames();
//        while (headerNames.hasMoreElements()) {
//            String name = headerNames.nextElement();
//            headerList.add(name);
//        }
//        log.info("{}",headerList);
        String fromSource = request.getHeader("from-source");
        //判断如果是feign
        if(StringUtils.equals(SecurityConstants.INNER,fromSource)){
            String stamp = request.getHeader("from-source-stamp");
            String auth = request.getHeader("from-source-auth");
            if(StringUtils.isNotBlank(stamp) && StringUtils.isNotBlank(auth) && StringUtils.equals(auth,DigestUtil.md5Hex(String.format("%s%s","inner",stamp)))){
                chain.doFilter(request, response);
                return;
            }
        }

        // 跳过不需要验证的路径
        if (StringUtils.matches(url, ignoreWhite.getWhites()))
        {
            chain.doFilter(request, response);
            return;
        }
        HeaderAddingRequestWrapper wrappedRequest = new HeaderAddingRequestWrapper(request);
        String token = getToken(request);
        if(!checkAuth){
            if(StringUtils.isEmpty(token)){
                chain.doFilter(wrappedRequest, response);
                return;
            }
        }

        if(StringUtils.isBlank(token)){
            String code = String.valueOf(HttpStatus.UNAUTHORIZED);
            String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
            ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, msg)));
            return;
        }

        Claims claims = JwtUtils.parseToken(token);

//        if (claims == null)
//        {
//            String code = String.valueOf(HttpStatus.UNAUTHORIZED);
//            String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
//            ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, msg)));
//        }

        if(claims.containsKey(SecurityConstants.CUST_ID)){
            LoginCust loginCust = custTokenService.getLoginCust(request);
            if (ObjUtil.isNotNull(loginCust)){
                custTokenService.verifyToken(loginCust);

                // 设置用户信息到请求
                String custKey = getTokenKey(loginCust.getToken());
                wrappedRequest.addHeader(SecurityConstants.CUST_KEY,custKey);
                wrappedRequest.addHeader(SecurityConstants.CUST_ID, loginCust.getCustId());
                wrappedRequest.addHeader(SecurityConstants.NICK_NAME, loginCust.getNickName());
            }else {
                if(!checkAuth){
                    chain.doFilter(wrappedRequest, response);
                    return;
                }
                String code = String.valueOf(HttpStatus.UNAUTHORIZED);
                String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
                ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, msg)));
                return;
            }


        }else{
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (ObjUtil.isNotNull(loginUser))
            {
                tokenService.verifyToken(loginUser);
                SysUser sysUser = loginUser.getSysUser();
                String userKey = getTokenKey(loginUser.getToken());
                wrappedRequest.addHeader(SecurityConstants.USER_KEY, userKey);
                wrappedRequest.addHeader(SecurityConstants.DETAILS_USER_ID, String.valueOf(sysUser.getUserId()));
                wrappedRequest.addHeader(SecurityConstants.DETAILS_USERNAME, String.valueOf(sysUser.getUserName()));
            }else {
                if(!checkAuth){
                    chain.doFilter(wrappedRequest, response);
                    return;
                }
                String code = String.valueOf(HttpStatus.UNAUTHORIZED);
                String msg = StringUtils.format("请求访问：{}，认证失败，无法访问系统资源", request.getRequestURI());
                ServletUtils.renderString(response, JSON.toJSONString(AjaxResult.error(code, msg)));
                return;
            }
        }



        chain.doFilter(wrappedRequest, response);
    }



    /**
     * 获取缓存key
     */
    private String getTokenKey(String token)
    {
        return CacheConstants.LOGIN_TOKEN_KEY + token;
    }

    /**
     * 获取请求token
     */
    private String getToken(HttpServletRequest request)
    {
        String token = request.getHeader(TokenConstants.AUTHENTICATION);
        // 如果前端设置了令牌前缀，则裁剪掉前缀
        if (StringUtils.isNotEmpty(token) && token.startsWith(TokenConstants.PREFIX))
        {
            token = token.replaceFirst(TokenConstants.PREFIX, StringUtils.EMPTY);
        }
        return token;
    }


    private static class HeaderAddingRequestWrapper extends HttpServletRequestWrapper {

        private final Map<String, String> customHeaders;

        public HeaderAddingRequestWrapper(HttpServletRequest request) {
            super(request);
            // 初始化自定义头信息 Map
            this.customHeaders = new HashMap<>();
            // 复制原始请求中的所有头信息到 customHeaders
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                this.customHeaders.put(headerName, headerValue);
            }
        }

        public void addHeader(String name, String value) {
            customHeaders.put(name, value);
        }

        @Override
        public String getHeader(String name) {
            String customHeaderValue = customHeaders.get(name);
            if (customHeaderValue != null) {
                return customHeaderValue;
            }
            return super.getHeader(name);
        }

        @Override
        public Enumeration<String> getHeaders(String name) {
            // Implement logic to handle multiple headers with the same name if necessary
            // This is a simple implementation that only handles single value for simplicity
            String customHeaderValue = customHeaders.get(name);
            if (customHeaderValue != null) {
                Vector<String> headers = new Vector<>();
                headers.add(customHeaderValue);
                return headers.elements();
            }
            return super.getHeaders(name);
        }

        @Override
        public Enumeration<String> getHeaderNames() {
            Set<String> set = new HashSet<>(customHeaders.keySet());
            Enumeration<String> e = super.getHeaderNames();
            while (e.hasMoreElements()) {
                String n = e.nextElement();
                set.add(n);
            }
            return Collections.enumeration(set);
        }
    }




}
