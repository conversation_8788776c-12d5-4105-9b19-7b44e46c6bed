package com.tianmasys.ebu.qingma.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *  emqx 客户端信息-返回
 */
@Data
public class EmqxClientRespVo implements Serializable {



    private List<ClientItem> list;


    // 分页大小
    @JSONField(name="limit")
    private Integer pageSize;

    // 分页页码
    @JSONField(name="page")
    private Integer pageNum;

    //总条数
    @JSONField(name="count")
    private Integer total;

    @Data
   public class ClientItem{
        // 客户端id,模糊搜索
        private  String clientid;

        //是否连接
        private  Boolean connected;

        //连接时间
        @JSONField(name="connected_at",format = "yyyy-MM-dd HH:mm:ss")
        private String connectedAt;

        //创建时间
        @JSONField(name="created_at",format = "yyyy-MM-dd HH:mm:ss")
        private String createdAt;


        //IP地址
        @JSONField(name="ip_address")
        private String ipAddress;

        //客户端端口
        private String port;

        //是否绑定过
        private Boolean isBind;

    }


}
