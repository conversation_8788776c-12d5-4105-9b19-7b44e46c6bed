package com.tianmasys.ebu.qingma.service;

import java.util.List;
import com.tianmasys.ebu.qingma.vo.ModbusPointsVO;
import com.tianmasys.ebu.qingma.domain.ModbusPointsEntity;
import com.baomidou.mybatisplus.extension.service.IService;


/**
 * modbus点位Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */
public interface IModbusPointsService extends IService<ModbusPointsEntity>
{
    /**
     * 查询modbusPoints
     * 
     * @param id modbusPoints主键
     * @return modbusPoints
     */
    public ModbusPointsVO selectModbusPointsById(Long id);

    /**
     * 查询modbusPoints列表
     * 
     * @param modbusPointsVO modbusPoints
     * @return modbusPoints集合
     */
    public List<ModbusPointsVO> selectModbusPointsList(ModbusPointsVO modbusPointsVO);

    /**
     * 新增modbusPoints
     * 
     * @param modbusPointsVO modbusPoints
     * @return 结果
     */
    public int insertModbusPoints(ModbusPointsVO modbusPointsVO);

    /**
     * 修改modbusPoints
     * 
     * @param modbusPointsVO modbusPoints
     * @return 结果
     */
    public int updateModbusPoints(ModbusPointsVO modbusPointsVO);

    /**
     * 批量删除modbusPoints
     * 
     * @param ids 需要删除的modbusPoints主键集合
     * @return 结果
     */
    public int deleteModbusPointsByIds(Long[] ids);

    /**
     * 删除modbusPoints信息
     * 
     * @param id modbusPoints主键
     * @return 结果
     */
    public int deleteModbusPointsById(Long id);
}
