package com.tianmasys.ebu.qingma.service;

import java.util.List;
import com.tianmasys.ebu.qingma.vo.ProductVO;
import com.tianmasys.ebu.qingma.domain.ProductEntity;
import com.baomidou.mybatisplus.extension.service.IService;


/**
 * iot产品表Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */
public interface IProductService extends IService<ProductEntity>
{
    /**
     * 查询product
     * 
     * @param id product主键
     * @return product
     */
    public ProductVO selectProductById(Long id);

    /**
     * 查询product列表
     * 
     * @param productVO product
     * @return product集合
     */
    public List<ProductVO> selectProductList(ProductVO productVO);

    /**
     * 新增product
     * 
     * @param productVO product
     * @return 结果
     */
    public int insertProduct(ProductVO productVO);

    /**
     * 修改product
     * 
     * @param productVO product
     * @return 结果
     */
    public int updateProduct(ProductVO productVO);

    /**
     * 批量删除product
     * 
     * @param ids 需要删除的product主键集合
     * @return 结果
     */
    public int deleteProductByIds(Long[] ids);

    /**
     * 删除product信息
     * 
     * @param id product主键
     * @return 结果
     */
    public int deleteProductById(Long id);
}
