<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tianmasys.ebu.qingma.mapper.DeviceMapper">

    <resultMap type="com.tianmasys.ebu.qingma.domain.DeviceEntity" id="deviceMap">
        <result property="id" column="id"/>
        <result property="deviceKey" column="device_key"/>
        <result property="deviceName" column="device_name"/>
        <result property="nickname" column="nickname"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="productId" column="product_id"/>
        <result property="productKey" column="product_key"/>
        <result property="deviceType" column="device_type"/>
        <result property="status" column="status"/>
        <result property="gatewayId" column="gateway_id"/>
        <result property="statusLastUpdateTime" column="status_last_update_time"/>
        <result property="lastOnlineTime" column="last_online_time"/>
        <result property="lastOfflineTime" column="last_offline_time"/>
        <result property="activeTime" column="active_time"/>
        <result property="ip" column="ip"/>
        <result property="firmwareVersion" column="firmware_version"/>
        <result property="deviceSecret" column="device_secret"/>
        <result property="mqttClientId" column="mqtt_client_id"/>
        <result property="mqttUsername" column="mqtt_username"/>
        <result property="mqttPassword" column="mqtt_password"/>
        <result property="authType" column="auth_type"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="areaId" column="area_id" typeHandler="com.tianmasys.ebu.common.mybatis.core.type.StringListTypeHandler"/>
        <result property="address" column="address"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="custId" column="cust_id"/>
    </resultMap>

</mapper>