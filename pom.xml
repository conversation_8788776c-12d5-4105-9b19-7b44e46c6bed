<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tianmasys.ebu</groupId>
        <artifactId>ebu-parent</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>ebu-qingma-bim</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>ebu-qingma-bim-server</module>
    </modules>


    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>2.7.14</spring-boot.version>

        <jsch.version>0.1.55</jsch.version>


        <ebu.version>2.0.0-SNAPSHOT</ebu.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <mysql.version>8.3.0</mysql.version>

        <mqtt.version>1.2.5</mqtt.version>

    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- 版本依赖管理 -->
            <dependency>
                <groupId>com.tianmasys.ebu</groupId>
                <artifactId>ebu-bom</artifactId>
                <version>${ebu.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Spring Boot Starter Web -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-quartz</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>


            <!-- Spring Boot Starter Test -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>1.9.7</version>
                <scope>compile</scope>
            </dependency>

            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>${jsch.version}</version>
            </dependency>

            <!--验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- ebu Module System -->
            <dependency>
                <groupId>com.tianmasys.ebu</groupId>
                <artifactId>ebu-module-system</artifactId>
                <version>${ebu.version}</version>
            </dependency>

            <!-- ebu Module Portal -->
            <dependency>
                <groupId>com.tianmasys.ebu</groupId>
                <artifactId>ebu-module-portal</artifactId>
                <version>${ebu.version}</version>
            </dependency>

            <!-- ebu Module file -->
            <dependency>
                <groupId>com.tianmasys.ebu</groupId>
                <artifactId>ebu-module-file</artifactId>
                <version>${ebu.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>


            <!-- ebu Common Mybatis -->
            <dependency>
                <groupId>com.tianmasys.ebu</groupId>
                <artifactId>ebu-common-mybatis</artifactId>
                <version>${ebu.version}</version>
            </dependency>


            <!-- MQTT -->
            <dependency>
                <groupId>org.eclipse.paho</groupId>
                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
                <version>${mqtt.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <finalName>${project.artifactId}</finalName>
                    <mainClass>com.tianmasys.ebu.tools.EbuToolsApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <!-- 先指定 src/main/resources下所有文件及文件夹为资源文件 -->
            <resource>
                <directory>src/main/resources</directory>
                <targetPath>${project.build.directory}/classes</targetPath>
                <includes>
                    <include>**/*</include>
                </includes>
                <excludes>
                    <exclude>env/**</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <!-- 根据env部署环境值，把对应环境的配置文件拷贝到classes目录 -->
            <resource>
                <directory>src/main/resources/env/${env}</directory>
                <targetPath>${project.build.directory}/classes</targetPath>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>


</project>