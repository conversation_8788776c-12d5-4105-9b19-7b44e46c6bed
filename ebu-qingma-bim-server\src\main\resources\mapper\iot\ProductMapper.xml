<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tianmasys.ebu.qingma.mapper.ProductMapper">

    <resultMap type="com.tianmasys.ebu.qingma.domain.ProductEntity" id="productMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="productKey" column="product_key"/>
        <result property="categoryId" column="category_id"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="deviceType" column="device_type"/>
        <result property="netType" column="net_type"/>
        <result property="protocolType" column="protocol_type"/>
        <result property="protocolId" column="protocol_id"/>
        <result property="dataFormat" column="data_format"/>
        <result property="validateType" column="validate_type"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

</mapper>