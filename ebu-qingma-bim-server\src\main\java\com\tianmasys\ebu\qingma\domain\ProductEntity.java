package com.tianmasys.ebu.qingma.domain;

import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

/**
 * iot产品表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */

@Data
@TableName("iot_product")
public class ProductEntity extends BaseEntity {
	/**
	* 产品ID
	*/
	@TableId
	private Long id;

	/**
	* 产品名称
	*/
	private String name;

	/**
	* 产品标识
	*/
	private String productKey;

	/**
	* 产品所属品类编号
	*/
	private String categoryId;

	/**
	* 产品描述
	*/
	private String description;

	/**
	* 产品状态:0-开发中;1-已发布;
	*/
	private String status;

	/**
	* 设备类型:0-直连设备;1-网关子设备;2-网关设备;
	*/
	private String deviceType;

	/**
	* 联网方式:0-WiFi;1-Cellular;2-Ethernet;3-其他;
	*/
	private String netType;

	/**
	* 接入网关协议:0-自定义;1-Modbus;2-<PERSON><PERSON> UA;3-ZigBee;4-BLE;
	*/
	private String protocolType;

	/**
	* 协议编号
	*/
	private Long protocolId;

	/**
	* 数据格式:0-标准数据格式（JSON）;1-透传/自定义;
	*/
	private String dataFormat;

	/**
	* 数据校验级别:0-弱校验;1-免校验
	*/
	private String validateType;

//	/**
//	* 是否删除：0-正常；1-删除；
//	*/
//	private String deleteFlag;
//
//	/**
//	* 创建时间
//	*/
//	private Date createTime;
//
//	/**
//	* 创建用户id
//	*/
//	private String createBy;
//
//	/**
//	* 修改时间
//	*/
//	private Date updateTime;
//
//	/**
//	* 修改用户id
//	*/
//	private String updateBy;
//
//	/**
//	* 租户编号
//	*/
//	private Long tenantId;

}