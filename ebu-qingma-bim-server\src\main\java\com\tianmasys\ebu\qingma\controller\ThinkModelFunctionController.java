package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.ThinkModelFunctionVO;
import com.tianmasys.ebu.qingma.service.IThinkModelFunctionService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * iot产品物模型功能Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-01-17
 */
@RestController
@RequestMapping("/thinkModelFunction")
public class ThinkModelFunctionController extends BaseController
{
    @Autowired
    private IThinkModelFunctionService thinkModelFunctionService;

    /**
     * 查询iot产品物模型功能列表
     */
    @RequiresPermissions("iot:thinkModelFunction:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<ThinkModelFunctionVO>> list(ThinkModelFunctionVO thinkModelFunctionVO)
    {
        startPage();
        List<ThinkModelFunctionVO> list = thinkModelFunctionService.selectThinkModelFunctionList(thinkModelFunctionVO);
        return success(getDataTable(list));
    }


    /**
     * 导出iot产品物模型功能列表
     */
    @RequiresPermissions("iot:thinkModelFunction:export")
    @Log(title = "iot产品物模型功能", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ThinkModelFunctionVO thinkModelFunctionVO)
    {
        List<ThinkModelFunctionVO> list = thinkModelFunctionService.selectThinkModelFunctionList(thinkModelFunctionVO);
        ExcelUtil<ThinkModelFunctionVO> util = new ExcelUtil<ThinkModelFunctionVO>(ThinkModelFunctionVO.class);
        util.exportExcel(response, list, "iot产品物模型功能数据");
    }

    /**
     * 获取iot产品物模型功能详细信息
     */
    @RequiresPermissions("iot:thinkModelFunction:query")
    @GetMapping(value = "/{id}")
    public AjaxResult<ThinkModelFunctionVO> getInfo(@PathVariable("id") Long id)
    {
        return success(thinkModelFunctionService.selectThinkModelFunctionById(id));
    }

    /**
     * 新增iot产品物模型功能
     */
    @RequiresPermissions("iot:thinkModelFunction:add")
    @Log(title = "iot产品物模型功能", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<ThinkModelFunctionVO> add(@Valid @RequestBody ThinkModelFunctionVO thinkModelFunctionVO)
    {
        return toAjax(thinkModelFunctionService.insertThinkModelFunction(thinkModelFunctionVO));
    }

    /**
     * 修改iot产品物模型功能
     */
    @RequiresPermissions("iot:thinkModelFunction:edit")
    @Log(title = "iot产品物模型功能", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<ThinkModelFunctionVO> edit(@Valid @RequestBody ThinkModelFunctionVO thinkModelFunctionVO)
    {
        return toAjax(thinkModelFunctionService.updateThinkModelFunction(thinkModelFunctionVO));
    }

    /**
     * 删除iot产品物模型功能
     */
    @RequiresPermissions("iot:thinkModelFunction:remove")
    @Log(title = "iot产品物模型功能", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<ThinkModelFunctionVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(thinkModelFunctionService.deleteThinkModelFunctionByIds(ids));
    }
}
