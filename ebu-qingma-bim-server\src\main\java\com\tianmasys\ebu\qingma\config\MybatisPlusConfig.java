//package com.tianmasys.ebu.qingma.config;
//
//import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
//import com.tianmasys.ebu.common.mybatis.core.handler.DefaultDBFieldHandler;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
//@Component
//public class MybatisPlusConfig {
//
//
//    @Bean
//    public MetaObjectHandler defaultMetaObjectHandler(){
//        return new DefaultDBFieldHandler();
//    }
//}
