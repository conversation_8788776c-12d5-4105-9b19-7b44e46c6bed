package com.tianmasys.ebu.qingma.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;

import java.util.HashMap;
import java.util.Map;

public class MessageUtils {


	public static String parseMessage(Map data, String templateContent) {
		//自动根据用户引入的模板引擎库的jar来自动选择使用的引擎
		//TemplateConfig为模板引擎的选项，可选内容有字符编码、模板路径、模板加载方式等，默认通过模板字符串渲染
		TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());

		//假设我们引入的是Beetl引擎，则：
		Template template = engine.getTemplate(templateContent);
		//Dict本质上为Map，此处可用Map
		String result = template.render(data);
		return result;
	}
	public static String parseMessageObj(String templateContent,Object... dataObj) {
		Map data = new HashMap();
		if (ArrayUtil.isNotEmpty(dataObj)) {
			for (Object obj : dataObj) {
				 data.putAll(BeanUtil.beanToMap(obj));
			}
		}

		//自动根据用户引入的模板引擎库的jar来自动选择使用的引擎
		//TemplateConfig为模板引擎的选项，可选内容有字符编码、模板路径、模板加载方式等，默认通过模板字符串渲染
		TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig());

		//假设我们引入的是Beetl引擎，则：
		Template template = engine.getTemplate(templateContent);
		//Dict本质上为Map，此处可用Map
		String result = template.render(data);
		return result;
	}

	public static void main(String[] args) {
		Map data = new HashMap();
		data.put("name", "张三");
		data.put("address_20", 18);
		data.put("sex", "男");
		data.put("address", "北京");
		data.put("phone", "1234567890");
		data.put("email", "<EMAIL>");
		data.put("birthday", "2020-01-01");
		data.put("hobby", "football");

		String templateContent = "姓名：${name}，年龄：${address_20}，性别：${sex}，地址：${address}，手机：${phone}，邮箱：${email}，生日：${birthday}，爱好：${hobby}";
//		String templateContent = "{\"humidity\":{\"value\":${\"20\"},\"unit\":\"%\",\"trend\":\"up\",\"status\":\"normal\"}}";

		String result = parseMessage(data, templateContent);
		System.out.println(result);
	}
}