<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tianmasys.ebu.qingma.mapper.DeviceEventMapper">

    <resultMap type="com.tianmasys.ebu.qingma.domain.DeviceEventEntity" id="deviceEventMap">
        <result property="id" column="id"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceKey" column="device_key"/>
        <result property="deviceName" column="device_name"/>
        <result property="nickname" column="nickname"/>
        <result property="productId" column="product_id"/>
        <result property="productKey" column="product_key"/>
        <result property="deviceType" column="device_type"/>
        <result property="eventType" column="event_type"/>
        <result property="eventIsPush" column="event_is_push"/>
        <result property="msgTemplateId" column="msg_template_id"/>
        <result property="eventMsg" column="event_msg"/>
        <result property="eventTime" column="event_time"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

</mapper>