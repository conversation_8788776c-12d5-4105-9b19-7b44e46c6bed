package com.tianmasys.ebu.qingma.service;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.tianmasys.ebu.common.core.constant.SecurityConstants;
import com.tianmasys.ebu.common.core.constant.UserConstants;
import com.tianmasys.ebu.common.core.domain.R;
import com.tianmasys.ebu.common.core.enums.UserStatus;
import com.tianmasys.ebu.common.core.exception.BusinessException;
import com.tianmasys.ebu.common.core.exception.ServiceException;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.system.api.RemoteCustFeignService;
import com.tianmasys.ebu.system.api.domain.CustAuthReqDto;
import com.tianmasys.ebu.system.api.domain.WxCustCode;
import com.tianmasys.ebu.system.api.domain.cust.CustAuthDto;
import com.tianmasys.ebu.system.api.model.LoginCust;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 */
@Component
public class CustLoginService {


    @Autowired
    private RemoteCustFeignService remoteCustFeignService;


    @Autowired
    private SysPasswordService passwordService;

    /**
     * 微信小程序快速登录
     */
    public LoginCust loginForCode(String code,String appId,Boolean isRegister){


        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(code, appId))
        {
            throw new ServiceException("参数有误,请检查！");
        }
        WxCustCode wxCustCode = new WxCustCode();
        wxCustCode.setCode(code);
        wxCustCode.setAppId(appId);
        wxCustCode.setIsRegister(isRegister);
        R<LoginCust> cust =  remoteCustFeignService.loginForCode(wxCustCode, SecurityConstants.INNER);
        if(!StringUtils.equals("200",cust.getCode())){
            throw  new BusinessException(cust.getCode(), cust.getMsg());
        }

        return cust.getData();

    }
    /**
     * 客户登录
     * 使用密码、短信验证码等方式进行登录
     *
     * @param appId 客户开通渠道主键
     * @param authType 客户开通渠道主键
     * @param param 请求参数
     * @return 结果
     */
    public LoginCust loginForAuth(String appId, String authType, JSONObject param){


        if("01".equals(authType)){//密码登录
            String username = param.getString("username");
            String password = param.getString("password");

            // 用户名或密码为空 错误
            if (StringUtils.isAnyBlank(username, password))
            {
//                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
                throw new ServiceException("用户/密码必须填写");
            }
            // 密码如果不在指定范围内 错误
            if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                    || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
            {
//                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
                throw new ServiceException("用户密码不在指定范围");
            }
            // 用户名不在指定范围内 错误
            if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                    || username.length() > UserConstants.USERNAME_MAX_LENGTH)
            {
//                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
                throw new ServiceException("用户名不在指定范围");
            }
            CustAuthReqDto dto = BeanUtil.toBean(param, CustAuthReqDto.class);
            dto.setAppId(appId);
            dto.setAuthType(authType);
            // 查询用户信息
            R<LoginCust> custResult = remoteCustFeignService.getLoginCust(dto, SecurityConstants.INNER);

            if (StringUtils.isNull(custResult) || StringUtils.isNull(custResult.getData()))
            {
//                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在");
                throw new ServiceException("登录用户：" + username + " 不存在");
            }

            if (R.FAIL.equals(custResult.getCode()))
            {
                throw new ServiceException(custResult.getMsg());
            }

            LoginCust loginCust = custResult.getData();
            List<CustAuthDto> custAuthList = loginCust.getCustAuthList();
            CustAuthDto custAuth = custAuthList.get(0);

            if (UserStatus.DELETED.getCode().equals(custAuth.getDeleteFlag()))
            {
//                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
                throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
            }
            if (UserStatus.DISABLE.getCode().equals(custAuth.getAuthState()))
            {
//                recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
                throw new ServiceException("对不起，您的账号：" + username + " 已停用");
            }
            passwordService.validateCust(custAuth, password);
//            recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");

            return loginCust;
        }
        return null;

    }



}
