---
type: "always_apply"
---

# 青马大桥BIM系统 - 项目开发规则

## 1. 项目概述

### 1.1 项目简介
青马大桥BIM系统是一个基于SpringBoot的Modbus数据采集与实时监控平台，主要功能包括：
- Modbus设备数据采集
- WebSocket实时数据推送
- 数据统计分析
- 设备管理与监控

### 1.2 技术栈
- **后端框架**: Spring Boot 2.x
- **数据库**: MySQL + MyBatis Plus
- **通信协议**: Modbus4j、WebSocket、MQTT
- **构建工具**: Maven
- **Java版本**: JDK 1.8

## 2. 项目结构规范

### 2.1 包结构规范
```
com.tianmasys.ebu.qingma
├── config/              # 配置类
│   ├── websocket/       # WebSocket配置
│   ├── properties/      # 配置属性类
│   └── feign/          # Feign配置
├── constant/           # 常量定义
├── controller/         # 控制器层
│   ├── app/           # 移动端API
│   └── common/        # 通用控制器
├── dataprocess/       # 数据处理层
│   ├── modbus/        # Modbus数据处理
│   └── emq/          # MQTT数据处理
├── domain/            # 实体类
├── mapper/            # 数据访问层
├── service/           # 业务逻辑层
│   └── impl/         # 业务实现类
├── statistics/        # 统计数据处理
├── job/              # 定时任务
├── utils/            # 工具类
├── vo/               # 视图对象
└── filter/           # 过滤器
```

### 2.2 文件命名规范
- **实体类**: `XxxEntity.java`
- **VO类**: `XxxVO.java`
- **控制器**: `XxxController.java`
- **服务接口**: `IXxxService.java`
- **服务实现**: `XxxServiceImpl.java`
- **Mapper接口**: `XxxMapper.java`
- **配置类**: `XxxConfig.java`

## 3. 代码规范

### 3.1 类和方法规范
```java
/**
 * 类注释模板
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */
@Slf4j
@Service
public class ModbusDataServiceImpl implements IModbusDataService {
    
    /**
     * 方法注释模板
     * 
     * @param siteId 站点ID
     * @param data 数据对象
     * @return 处理结果
     */
    public boolean processData(String siteId, Object data) {
        // 方法实现
    }
}
```

### 3.2 注解使用规范
- **实体类**: 使用`@Data`、`@TableName`、`@TableId`
- **服务类**: 使用`@Service`、`@Slf4j`
- **控制器**: 使用`@RestController`、`@RequestMapping`
- **配置类**: 使用`@Configuration`、`@EnableXxx`
- **定时任务**: 使用`@Component`、`@PostConstruct`

### 3.3 异常处理规范
```java
try {
    // 业务逻辑
} catch (Exception e) {
    log.error("操作失败: {}", e.getMessage(), e);
    throw new ServiceException("操作失败");
}
```

## 4. 数据库规范

### 4.1 表命名规范
- 使用小写字母和下划线
- 表名前缀：`iot_`（设备相关）、`modbus_`（Modbus相关）
- 示例：`iot_device`、`modbus_data_log`、`modbus_site`

### 4.2 字段命名规范
- 主键：`id`（BIGINT AUTO_INCREMENT）
- 创建时间：`create_time`
- 更新时间：`update_time`
- 创建人：`create_by`
- 更新人：`update_by`
- 删除标志：`delete_flag`

### 4.3 MyBatis Plus配置
```yaml
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  typeAliasesPackage: com.tianmasys.ebu.*.domain
  global-config:
    db-config:
      id-type: AUTO
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
```

## 5. Modbus数据采集规范

### 5.1 配置结构
```java
// 站点配置
ModbusSiteEntity site = new ModbusSiteEntity();
site.setSiteId(1L);
site.setHost("*************");
site.setPort(502);

// 点位配置
ModbusPointsEntity point = new ModbusPointsEntity();
point.setAddress(1001);
point.setDataType("HOLDING_REGISTER");
```

### 5.2 数据读取规范
- 使用`Modbus4jUtils`工具类进行数据读取
- 支持批量读取提高效率
- 异常处理和重连机制
- 数据格式：`address_1001: value`

### 5.3 定时任务规范
```java
@PostConstruct
public void startScheduledTask() {
    taskSchedulerManager.registerFixedRateTask(
        "modbusDataJob", 
        this::readModbusDataAndSend, 
        0, 
        1000  // 1秒间隔
    );
}
```

## 6. WebSocket通信规范

### 6.1 配置规范
```java
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(webSocketHandler, "/ws")
                .setAllowedOriginPatterns("*")
                .withSockJS();
    }
}
```

### 6.2 数据推送格式
```json
{
    "timestamp": "2025-08-08T10:30:00",
    "data": {
        "siteId_1": {
            "address_1001": 100,
            "address_1002": 200
        }
    }
}
```

### 6.3 会话管理
- 使用`ConcurrentHashMap`存储会话
- 自动清理断开的连接
- 支持广播和单播

## 7. 统计数据处理规范

### 7.1 统计类型
- **分钟统计**: 每分钟整点保存
- **小时统计**: 每小时整点保存  
- **天统计**: 每天整点保存

### 7.2 统计指标
```java
Map<String, Object> pointResult = new HashMap<>();
pointResult.put("avg", average);      // 平均值
pointResult.put("last", lastValue);   // 最新值
pointResult.put("count", count);      // 数据点数量
```

### 7.3 数据存储
```java
ModbusDataStatisticsEntity entity = new ModbusDataStatisticsEntity();
entity.setSiteId(siteId);
entity.setStatType("minute"); // minute/hour/day
entity.setStartTime(periodStartTime);
entity.setEndTime(periodEndTime);
entity.setData(statResult);
```

## 8. 配置管理规范

### 8.1 多环境配置
- **开发环境**: `env/dev/application.yml`
- **测试环境**: `env/test/application.yml`
- **生产环境**: `env/prod/application.yml`

### 8.2 配置项规范
```yaml
# 安全配置
security:
  ignore:
    whites:
      - /auth/login
      - /ws/**
      - /iot/device/dealMqttEvent

# 业务路由配置
biz:
  routes:
    - id: ebu-iot
      uri: /
      pathPrefix: /iot/**
      prefixToRemove: /iot/
```

## 9. 日志规范

### 9.1 日志级别
- **ERROR**: 系统错误、异常
- **WARN**: 警告信息
- **INFO**: 重要业务信息
- **DEBUG**: 调试信息

### 9.2 日志格式
```java
log.info("Modbus数据读取成功, 站点: {}, 数据量: {}", siteId, dataSize);
log.error("WebSocket发送失败: {}", e.getMessage(), e);
log.debug("处理站点 {} 数据: {}", siteId, data);
```

## 10. 性能优化规范

### 10.1 并发处理
- 使用`ConcurrentHashMap`存储共享数据
- 异步处理数据保存：`@Async`
- 并发读取Modbus数据

### 10.2 内存管理
- 定期清理过期数据
- 使用对象池减少GC压力
- 合理设置缓存大小

### 10.3 数据库优化
- 批量插入统计数据
- 合理使用索引
- 分页查询大数据量

## 11. 测试规范

### 11.1 单元测试
- 覆盖核心业务逻辑
- Mock外部依赖
- 测试异常场景

### 11.2 集成测试
- 测试Modbus连接
- 测试WebSocket通信
- 测试数据完整性

## 12. 部署规范

### 12.1 打包命令
```bash
# 开发环境
mvn clean package -Pdev

# 生产环境  
mvn clean package -Pprod
```

### 12.2 启动参数
```bash
java -jar -Xms512m -Xmx1024m ebu-qingma-bim-server.jar
```

## 13. 安全规范

### 13.1 认证授权
- 使用JWT Token认证
- 接口权限控制：`@RequiresPermissions`
- 白名单配置

### 13.2 数据安全
- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护

---

**版本**: v1.0  
**更新时间**: 2025-08-08  
**维护人**: 开发团队
