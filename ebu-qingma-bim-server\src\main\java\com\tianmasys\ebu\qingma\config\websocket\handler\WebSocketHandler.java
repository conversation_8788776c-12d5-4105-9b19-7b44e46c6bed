package com.tianmasys.ebu.qingma.config.websocket.handler;

import com.tianmasys.ebu.qingma.config.websocket.WebSocketService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WebSocketHandler extends TextWebSocketHandler {

    @Autowired
    private WebSocketService webSocketService;

    // 存储所有活跃的 WebSocket 会话
//    private static final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // 连接建立时的处理逻辑
        log.info("WebSocket connection established:{} " , session.getId());
        webSocketService.addSession(session);
        super.afterConnectionEstablished(session);

    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        // 处理客户端发送的消息（如果需要）
        String payload = message.getPayload();
        log.info("收到WebSocket消息: {}", payload);
        // 可以根据需要处理客户端发送的消息
        // 例如，客户端可以请求特定的数据
        super.handleTextMessage(session, message);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        // 连接关闭时的处理逻辑
        log.info("WebSocket connection closed: {} " , session.getId());
        webSocketService.removeSession(session);
        super.afterConnectionClosed(session, status);
    }
    
}
