package com.tianmasys.ebu.qingma.domain;

import lombok.Data;

@Data
public class DeviceTaskConfig {

    /**
     *  定时任务是否开启：0-关闭;1-开启;
     */
    private String isOpen;

    /**
     *  任务类型：1-除味;
     */
    private String taskType;

    /**
     * 工作开始时间 00-24小时制
     */
    private String workTimeStart;

    /**
     * 工作结束时间  00-24小时制
     */
    private String workTimeEnd;

    /**
     * 计时单位 ：1-秒;2-分;
     */
    private String timeUnit;

    /**
     * 开启时间
     */
    private String on;

    /**
     * 关闭时间
     */
    private String off;

    /**
     * 任务周期类型：1-一次性;2-重复;
     */
    private String taskPeriodType;


    /**
     * 任务周期日期
     * 如果任务周期类型为1，存具体日期
     * 如果任务周期类型为2，存周一至周日七位字符串 0000000, 0 表示关闭，1表示开启
     */
    private String workDay;


}
