package com.tianmasys.ebu.qingma.service;

import com.tianmasys.ebu.common.core.constant.CacheConstants;
import com.tianmasys.ebu.common.redis.service.RedisService;
import com.tianmasys.ebu.module.system.config.properties.CaptchaProperties;
import com.tianmasys.ebu.module.system.service.ISysPermissionService;
import com.tianmasys.ebu.module.system.service.ISysUserService;
import com.tianmasys.ebu.system.api.domain.SysUser;
import com.tianmasys.ebu.system.api.model.LoginUser;
//import com.tianmasys.ebu.system.service.ISysPermissionService;
//import com.tianmasys.ebu.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.tianmasys.ebu.common.core.constant.UserConstants;
import com.tianmasys.ebu.common.core.domain.R;
import com.tianmasys.ebu.common.core.enums.UserStatus;
import com.tianmasys.ebu.common.core.exception.ServiceException;
import com.tianmasys.ebu.common.core.utils.StringUtils;

import java.util.Set;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
//    @Autowired
//    private RemoteUserFeignService remoteUserFeignService;
//
    @Autowired
    private SysPasswordService passwordService;
//
//    @Autowired
//    private SysRecordLogService recordLogService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysPermissionService permissionService;

    @Autowired
    private CaptchaProperties captchaProperties;

    @Autowired
    private RedisService redisService;



    /**
     * 登录
     */
    public LoginUser login(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
            throw new ServiceException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }
        // 查询用户信息
//        R<LoginUser> userResult = remoteUserFeignService.getUserInfo(username, SecurityConstants.INNER);
        R<LoginUser> userResult = new R<>();

        SysUser sysUser = sysUserService.selectUserByUserName(username);
        LoginUser loginUser = null;
        if(sysUser != null ){
            loginUser = new LoginUser();
            loginUser.setSysUser(sysUser);
            userResult.setData(loginUser);
            // 角色集合
            Set<String> roles = permissionService.getRolePermission(sysUser);
            // 权限集合
            Set<String> permissions = permissionService.getMenuPermission(sysUser);
            loginUser.setPermissions(permissions);
            loginUser.setRoles(roles);
        }



        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData()))
        {
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在");
            throw new ServiceException("登录用户：" + username + " 不存在");
        }

        if (R.FAIL.equals(userResult.getCode()))
        {
            throw new ServiceException(userResult.getMsg());
        }

        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        passwordService.validate(user, password);
//        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");
        return userInfo;
    }

    public void logout(String loginName)
    {
//        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String username, String password)
    {
//        // 用户名或密码为空 错误
//        if (StringUtils.isAnyBlank(username, password))
//        {
//            throw new ServiceException("用户/密码必须填写");
//        }
//        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
//                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
//        {
//            throw new ServiceException("账户长度必须在2到20个字符之间");
//        }
//        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
//                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
//        {
//            throw new ServiceException("密码长度必须在5到20个字符之间");
//        }
//
//        // 注册用户信息
//        SysUser sysUser = new SysUser();
//        sysUser.setUserName(username);
//        sysUser.setNickName(username);
//        sysUser.setPassword(SecurityUtils.encryptPassword(password));
//        R<?> registerResult = remoteUserFeignService.registerUserInfo(sysUser, SecurityConstants.INNER);
//
//        if (R.FAIL == registerResult.getCode())
//        {
//            throw new ServiceException(registerResult.getMsg());
//        }
//        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");
    }


    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code 验证码
     * @param uuid 唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid)
    {
        boolean captchaEnabled = captchaProperties.getEnabled();
        if (captchaEnabled)
        {
            if (StringUtils.isEmpty(code))
            {
                throw new ServiceException("验证码不能为空");
            }
            String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
            String captcha = redisService.getCacheObject(verifyKey);
            if (captcha == null)
            {
//                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
                throw new ServiceException("验证码已失效");
            }
            redisService.deleteObject(verifyKey);
            if (!code.equalsIgnoreCase(captcha))
            {
//                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
                throw new ServiceException("验证码错误");
            }
        }
    }

}
