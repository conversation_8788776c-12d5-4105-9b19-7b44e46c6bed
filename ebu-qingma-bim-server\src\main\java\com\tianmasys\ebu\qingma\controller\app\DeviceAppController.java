package com.tianmasys.ebu.qingma.controller.app;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;
import com.tianmasys.ebu.common.security.utils.SecurityUtils;
import com.tianmasys.ebu.qingma.vo.DeviceCmdVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.qingma.vo.DeviceVO;
import com.tianmasys.ebu.qingma.service.IDeviceService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;

import javax.validation.Valid;

/**
 * iot设备表Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-01-17
 */
@RestController
@RequestMapping("/app/device")
public class DeviceAppController extends BaseController
{
    @Autowired
    private IDeviceService deviceService;

    /**
     * 查询iot设备表列表
     */
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<DeviceVO>> list(DeviceVO deviceVO)
    {
        startPage();
        String custId = SecurityUtils.getCustId();
        deviceVO.setCustId(custId);
        List<DeviceVO> list = deviceService.selectDeviceList(deviceVO);
        return success(getDataTable(list));
    }



    /**
     * 获取iot设备表详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult<DeviceVO> getInfo(@PathVariable("id") Long id)
    {
        return success(deviceService.selectDeviceById(id));
    }

    /**
     * 新增iot设备表
     */
    @PostMapping("/add")
    public AjaxResult<DeviceVO> add(@Valid @RequestBody DeviceVO deviceVO)
    {
        String custId = SecurityUtils.getCustId();
        deviceVO.setCustId(custId);
        return toAjax(deviceService.insertDevice(deviceVO));
    }

    /**
     * 修改iot设备表
     */
    @PostMapping("/edit")
    public AjaxResult<DeviceVO> edit(@Valid @RequestBody DeviceVO deviceVO)
    {
        return toAjax(deviceService.updateDevice(deviceVO));
    }

    /**
     * 删除iot设备表
     */
    @DeleteMapping("/{ids}")
    public AjaxResult<DeviceVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceService.deleteDeviceByIds(ids));
    }

    @PostMapping("/deviceCmd")
    public AjaxResult<?> deviceCmd(@RequestBody @Valid DeviceCmdVo deviceCmdVo)
    {
        JSONObject res = deviceService.deviceCmd(deviceCmdVo);
        return AjaxResult.success(res);
    }
}
