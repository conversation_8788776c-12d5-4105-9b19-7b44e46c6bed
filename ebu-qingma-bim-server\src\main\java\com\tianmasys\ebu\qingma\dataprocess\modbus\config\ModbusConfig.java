package com.tianmasys.ebu.qingma.dataprocess.modbus.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import com.tianmasys.ebu.qingma.dataprocess.modbus.client.ModbusClient;
import com.tianmasys.ebu.qingma.dataprocess.modbus.callback.impl.QingmaDataCallback;
import com.tianmasys.ebu.qingma.domain.ModbusConfigData;
import com.tianmasys.ebu.qingma.domain.ModbusPointsEntity;
import com.tianmasys.ebu.qingma.service.IModbusSiteService;
import lombok.Data;
import lombok.Setter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 配置类，modbus tcp 连接参数
 *
 * <AUTHOR>
 */
@Data
@Component
//@ConfigurationProperties("iot.modbus")
//@ConditionalOnProperty(name = "iot.modbus.masters[0].host")
public class ModbusConfig {

    /**
     * 主站配置
     */
    private List<MasterData> masters;



    private List<ModbusClient> modbusClients;


    @Resource
    //回调函数
    private QingmaDataCallback qingmaDataCallback;


    @Resource
    private IModbusSiteService modbusSiteService;

    @PostConstruct
    public void init(){
        List<ModbusConfigData> modbusConfigDataList = modbusSiteService.getModbusConfigDataList();
        List<MasterData> masterDataList = convert(modbusConfigDataList);
        this.setMasters(masterDataList);
    }


    public void initClient(){
        modbusClients = new LinkedList<>();
        masters.forEach(masterData -> {
            ModbusClient modbusClient = new ModbusClient(masterData);
            modbusClient.connect(qingmaDataCallback);
            modbusClients.add(modbusClient);
        });
    }


    public ModbusClient getClientByMasterId(String masterId){
        if(modbusClients == null){
            initClient();
        }

       return modbusClients.stream()
                .filter(client -> client.getMasterData().getId().equals(masterId))
                .findFirst()
                .orElse(null);
    }


    private List<MasterData> convert(List<ModbusConfigData> modbusConfigDataList) {
        List<MasterData> res = new ArrayList<>();


        for (ModbusConfigData modbusConfigData : modbusConfigDataList) {

            MasterData masterData = new MasterData();
            masterData.setId(String.valueOf(modbusConfigData.getId()));
            masterData.setHost(modbusConfigData.getHost());
            masterData.setPort(modbusConfigData.getPort());
            masterData.setKeepAlive(modbusConfigData.getKeepAlive());
            masterData.setSiteId(modbusConfigData.getSiteId().intValue());

            List<SlaveData> slaves = new ArrayList<>();
            List<ModbusPointsEntity> points = modbusConfigData.getPoints();
            if(points != null && points.size() > 0){

                points.stream()
                        .collect(java.util.stream.Collectors.groupingBy(
                                point -> point.getMasterId() + "_" + point.getAutoRead() + "_" + point.getFunctionCode() + "_" + point.getDataType(),
                                java.util.stream.Collectors.toList()))
                        .forEach((key, pointList) -> {
                            SlaveData slaveData = new SlaveData();
                            ModbusPointsEntity firstPoint = pointList.get(0);
                            slaveData.setSlaveId(firstPoint.getSlaveId().intValue());
                            slaveData.setDataType(firstPoint.getDataType());
                            slaveData.setFunctionCode(firstPoint.getFunctionCode());
                            slaveData.setAutoRead(firstPoint.getAutoRead());
                            slaveData.setInterval(firstPoint.getInterval());
                            slaveData.setMappingConfig(firstPoint.getMappingConfig());

                            List<Integer> offsetList = pointList.stream()
                                    .map(ModbusPointsEntity::getRegisterAddress)
                                    .collect(java.util.stream.Collectors.toList());
                            slaveData.setOffsetList(offsetList);

                            List<String> mappingConfigList = pointList.stream().map(ModbusPointsEntity::getMappingConfig).collect(Collectors.toList());
                            JSONArray jsonArray = new JSONArray(mappingConfigList);
                            slaveData.setMappingConfig(jsonArray.toJSONString(0));


                            slaves.add(slaveData);
                        });
                masterData.setSlaves(slaves);
            }
            if(slaves.size() >0 ){
                res.add(masterData);
            }
        }

        return res;
    }


}
