package com.tianmasys.ebu.qingma;

import com.tianmasys.ebu.common.security.annotation.EnableCustomConfig;
import com.tianmasys.ebu.common.security.annotation.EnableRyFeignClients;
import com.tianmasys.ebu.common.swagger.annotation.EnableCustomSwagger2;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 青马大桥-启动类
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@Slf4j
@ComponentScan(basePackages = {"com.tianmasys.ebu"})
//@MapperScan(basePackages = {"classpath:mapper/**/*.xml"})
@MapperScan("com.tianmasys.ebu.**.mapper")
@EnableAsync
//@EnableAutoConfiguration(exclude = {LoadBalancerAutoConfiguration.class})
public class EbuQingmaApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(EbuQingmaApplication.class, args);
        log.info("青马大桥管理平台-启动成功");
    }
}
