package com.tianmasys.ebu.qingma.domain;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import com.tianmasys.ebu.common.mybatis.core.type.JSONObjectListTypeHandler;
import com.tianmasys.ebu.common.mybatis.core.type.StringListTypeHandler;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import java.util.Date;
import java.util.List;

/**
 * iot设备表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */

@Data
@TableName(value = "iot_device", autoResultMap = true)
public class DeviceEntity extends BaseEntity {
	/**
	* 设备 ID
	*/
	@TableId
	private Long id;

	/**
	* 设备唯一标识符(全局唯一，用于识别设备)
	*/
	private String deviceKey;

	/**
	* 设备名称(在产品内唯一，用于标识设备)
	*/
	private String deviceName;

	/**
	* 设备备注名称
	*/
	private String nickname;

	/**
	* 设备序列号
	*/
	private String serialNumber;

	/**
	* 产品编号
	*/
	private Long productId;

	/**
	* 产品标识
	*/
	private String productKey;

	/**
	* 设备类型:0-直连设备;1-网关子设备;2-网关设备;
	*/
	private String deviceType;

	/**
	* 设备状态:0-未激活;1-在线;2-离线;3-已禁用;
	*/
	private String status;

	/**
	* 网关设备编号(子设备需要关联的网关设备 ID)
	*/
	private Long gatewayId;

	/**
	* 设备状态最后更新时间
	*/
	private Date statusLastUpdateTime;

	/**
	* 最后上线时间
	*/
	private Date lastOnlineTime;

	/**
	* 最后离线时间
	*/
	private Date lastOfflineTime;

	/**
	* 设备激活时间
	*/
	private Date activeTime;

	/**
	* 设备的IP地址
	*/
	private String ip;

	/**
	* 设备的固件版本
	*/
	private String firmwareVersion;

	/**
	* 设备密钥(用于设备认证,需安全存储)
	*/
	private String deviceSecret;

	/**
	* MQTT客户端ID
	*/
	private String mqttClientId;

	/**
	* MQTT用户名
	*/
	private String mqttUsername;

	/**
	* MQTT密码
	*/
	private String mqttPassword;

	/**
	* 认证类型:1-如一机一密;2-动态注册
	*/
	private String authType;

	/**
	* 设备位置的纬度
	*/
	private String latitude;

	/**
	* 设备位置的经度
	*/
	private String longitude;

	/**
	* 地区编码(关联Area的id)
	*/
	@TableField(value = "area_id", typeHandler = StringListTypeHandler.class)
	private List<String> areaId;

	/**
	* 设备详细地址
	*/
	private String address;

//	/**
//	* 是否删除：0-正常；1-删除；
//	*/
//	private String deleteFlag;
//
//	/**
//	* 创建时间
//	*/
//	private Date createTime;
//
//	/**
//	* 创建用户id
//	*/
//	private String createBy;
//
//	/**
//	* 修改时间
//	*/
//	private Date updateTime;
//
//	/**
//	* 修改用户id
//	*/
//	private String updateBy;
//
//	/**
//	* 租户编号
//	*/
//	private Long tenantId;

	/**
	 * 产品名称
	 */
	@TableField(exist = false)
	private String productName;

	/**
	 * 扩展配置
	 */
	@TableField(value = "extend_config", typeHandler = JacksonTypeHandler.class)
	private DeviceExtendConfig extendConfig;

	/**
	 * 任务配置
	 */
	@TableField(value = "task_config", typeHandler = JSONObjectListTypeHandler.class)
	private List<DeviceTaskConfig> taskConfigList;

	/**
	 * 客户编号
	 */
	private String custId;

	/**
	 * 设备排序
	 */
	private String deviceSort;

}