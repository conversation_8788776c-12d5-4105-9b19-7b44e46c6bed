package com.tianmasys.ebu.qingma.domain;

import com.tianmasys.ebu.common.core.web.domain.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 客户关联关系
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-02-21
 */

@Data
@TableName("iot_cust_relation")
public class CustRelationEntity extends BaseEntity {
	/**
	* 客户id
	*/
	@TableId
	private String custId;

	/**
	 * 用户名
	 */
	@TableField(exist = false)
	private String userName;

	/**
	 * 手机号码
	 */
	@TableField(exist = false)
	private String mobile;

	/**
	 * 昵称
	 */
	@TableField(exist = false)
	private String nickName;

	/**
	* 上级客户编号
	*/
	private String parentCustId;

	/**
	 * 上级用户名
	 */
	@TableField(exist = false)
	private String parentUserName;

	/**
	 * 上级昵称
	 */
	@TableField(exist = false)
	private String parentNickName;

	/**
	 * 上级手机号码
	 */
	@TableField(exist = false)
	private String parentMobile;

	/**
	* 层级：1-一级;2-二级;
	*/
	private Integer custLeave;


	/**
	 * 应用id
	 */
	private String appId;


}