package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 客户关联关系
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-02-21
 */
@Data
@Schema(description = "iot客户关联关系")
public class CustRelationVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "客户id")
	private String custId;

	@Schema(description = "用户名")
	private String userName;

	@Schema(description = "手机号码")
	private String mobile;

	@Schema(description = "昵称")
	private String nickName;

	@Schema(description = "上级客户编号")
	private String parentCustId;

	@Schema(description = "上级用户名")
	private String parentUserName;

	@Schema(description = "上级手机号码")
	private String parentMobile;

	@Schema(description = "上级昵称")
	private String parentNickName;

	@Schema(description = "层级：0-父级;1-一级;2-二级;")
	@NotNull(message = "层级不能为空!")
	private Integer custLeave;
	
	@Schema(description = "是否删除：0-正常；1-删除；")
	private String deleteFlag;
	
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	@Schema(description = "创建用户id")
	private String createBy;
	
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@Schema(description = "修改用户id")
	private String updateBy;
	
	@Schema(description = "租户编号")
	private Long tenantId;

	@Schema(description = "应用id")
	private String appId;
	



}