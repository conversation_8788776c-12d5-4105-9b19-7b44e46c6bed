package com.tianmasys.ebu.qingma.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import java.util.Date;

/**
 * modbus站点
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-08-06
 */
@Data
@Schema(description = "modbus站点")
public class ModbusSiteVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "主键ID")
	private Long id;
	
	@Schema(description = "站点id")
	@NotEmpty(message = "站点id不能为空!")
	private Long siteId;
	
	@Schema(description = "站点名称")
	@NotEmpty(message = "站点名称不能为空!")
	private String siteName;
	
	@Schema(description = "站点描述")
	@NotEmpty(message = "站点描述不能为空!")
	private String description;
	
	@Schema(description = "主站点id")
	@NotEmpty(message = "主站点id不能为空!")
	private Long masterId;
	
	@Schema(description = "主机地址")
	@NotEmpty(message = "主机地址不能为空!")
	private String host;
	
	@Schema(description = "端口")
	@NotEmpty(message = "端口不能为空!")
	private Integer port;
	
	@Schema(description = "是否长连接：0-否；1-是；")
	@NotEmpty(message = "是否长连接不能为空!")
	private Integer keepAlive;
	
	@Schema(description = "是否删除：0-正常；1-删除；")
	private String deleteFlag;
	
	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	@Schema(description = "创建用户id")
	private String createBy;
	
	@Schema(description = "修改时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@Schema(description = "修改用户id")
	private String updateBy;
	
	@Schema(description = "租户编号")
	private Long tenantId;
	



}