package com.tianmasys.ebu.qingma.config.feign;

import cn.hutool.crypto.digest.DigestUtil;
import com.tianmasys.ebu.common.core.utils.DateUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Target;
import lombok.Data;

import java.util.Map;
@Data
public class DynamicUrlInterceptor implements RequestInterceptor {


    private Map<String,String> feignRouteConfigMap;



    @Override
    public void apply(RequestTemplate template) {

        String name = template.feignTarget().name();
        // 添加校验
        String timeNow = DateUtils.dateTimeNow();
        template.header("from-source","inner");
        template.header("from-source-stamp",timeNow);
        template.header("from-source-auth", DigestUtil.md5Hex(String.format("%s%s","inner",timeNow)));
        if (feignRouteConfigMap.containsKey(name)) {
//            template.target(feignRouteConfigMap.get(name).getUrl());
//            String baseUrl = feignRouteConfigMap.get(name).getUrl();
//            // 获取原始的URL
//            URI originalUri = URI.create(template.request().url());
//            // 构建新的URL
//            String newUrl = baseUrl + originalUri.getPath();
//            // 替换URL
//            template.uri(newUrl);
            Target<?> target = template.feignTarget();
//            MethodMetadata methodMetadata = template.methodMetadata();


//
//            target.url();
//
//            template.feignTarget().url();

            String baseUrl = feignRouteConfigMap.get(name);
            Target.HardCodedTarget<?> destTarget = new Target.HardCodedTarget<>(target.type(), target.name(), baseUrl);
            template.feignTarget(destTarget);
            template.target(baseUrl);

//            template.uri(baseUrl);
        }


//        if (dynamicUrl != null) {
//            template.target(dynamicUrl);
//        }
    }
}