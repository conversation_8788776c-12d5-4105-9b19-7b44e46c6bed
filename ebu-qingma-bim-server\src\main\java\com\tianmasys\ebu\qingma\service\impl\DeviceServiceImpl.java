package com.tianmasys.ebu.qingma.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.tianmasys.ebu.common.core.constant.SecurityConstants;
import com.tianmasys.ebu.common.core.domain.R;
import com.tianmasys.ebu.common.core.exception.BusinessException;
import com.tianmasys.ebu.common.core.utils.DateUtils;
import com.tianmasys.ebu.common.core.utils.PageObjectConvertUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.tianmasys.ebu.common.core.utils.StringUtils;
import com.tianmasys.ebu.common.redis.service.RedisService;
import com.tianmasys.ebu.system.api.RemoteCustFeignService;
import com.tianmasys.ebu.system.api.domain.cust.CustAuthDto;
import com.tianmasys.ebu.qingma.constant.IotConst;
import com.tianmasys.ebu.qingma.domain.DeviceTaskConfig;
import com.tianmasys.ebu.qingma.domain.ProductEntity;
import com.tianmasys.ebu.qingma.dataprocess.emq.client.EmqxClient;
import com.tianmasys.ebu.qingma.dataprocess.emq.service.cmd.CmdProcessImpl;
import com.tianmasys.ebu.qingma.service.CommonCustService;
import com.tianmasys.ebu.qingma.service.IDeviceEventService;
import com.tianmasys.ebu.qingma.service.IProductService;
import com.tianmasys.ebu.qingma.vo.DeviceCmdVo;
import com.tianmasys.ebu.qingma.vo.EmqxClientReqVo;
import com.tianmasys.ebu.qingma.vo.EmqxClientRespVo;
import com.tianmasys.ebu.qingma.vo.ProductVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.tianmasys.ebu.qingma.mapper.DeviceMapper;
import com.tianmasys.ebu.qingma.service.IDeviceService;
import com.tianmasys.ebu.qingma.vo.DeviceVO;
import com.tianmasys.ebu.qingma.domain.DeviceEntity;

/**
 * iot设备表Service业务层处理
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */

@Service
@Slf4j
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, DeviceEntity> implements IDeviceService {
    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private IProductService productServiceImpl;

    @Autowired
    @Lazy
    private EmqxClient emqxClient;

    @Autowired
    @Lazy
    private CmdProcessImpl cmdProcessImpl;

    @Autowired
    private IDeviceEventService deviceEventServiceImpl;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CommonCustService commonCustService;

    @Autowired
    private RemoteCustFeignService remoteCustFeignService;


    private String preRedisKey = "device_state:";

    @Value("${iot.emq.http.key:}")
    private String apiKey;

    @Value("${iot.emq.http.secret:}")
    private String apiSecret;
    @Value("${iot.emq.http.url:}")
    private String apiUrl;

    /**
     * 查询iot设备表详情
     *
     * @param id iot设备表主键
     * @return iot设备表
     */
    @Override
    public DeviceVO selectDeviceById(Long id) {

//        //左连接查询
//        MPJLambdaWrapper<DeviceEntity> wrapper = new MPJLambdaWrapper<DeviceEntity>()
//                .selectAll(DeviceEntity.class) // 查询DeviceEntity类的所有字段
//                .selectAs(ProductEntity::getName, DeviceEntity::getProductName)// 查询Dept类的name字段
//                .leftJoin(ProductEntity.class, ProductEntity::getId, DeviceEntity::getProductId); // 左连接
//        wrapper.eq(DeviceEntity::getId, id);
        DeviceVO deviceVOReq = new DeviceVO();
        deviceVOReq.setId(id);
        DeviceEntity entity = deviceMapper.selectJoinOne(DeviceEntity.class,getWrapper(deviceVOReq));

        DeviceVO deviceVO = BeanUtil.toBean(entity, DeviceVO.class);
        String productKey = entity.getProductKey();
        List<Object> deviceList = new ArrayList<>();
        deviceList.add(entity.getDeviceName());
        List<JSONObject> multiCacheMapValue = redisService.getMultiCacheMapValue(preRedisKey + productKey, deviceList);
        Map<String, JSONObject> tmpMap = multiCacheMapValue.stream().filter(item -> item != null).collect(Collectors.toMap(item -> item.getString("devId"), item -> item));
        BeanUtil.copyProperties(tmpMap.get(entity.getDeviceName()), deviceVO);

        List<String> custIdList = new ArrayList<>();
        if(StringUtils.isNotBlank(deviceVO.getCustId())){
            custIdList.add(deviceVO.getCustId());
        }
        Map<String,CustAuthDto> custAuthMap = new HashMap<>();
        if(!custIdList.isEmpty()){
            R<List<CustAuthDto>> custAuthRes = remoteCustFeignService.getCustAuthByIds(custIdList.toArray(new String[]{}), SecurityConstants.INNER);
            if("200".equals(custAuthRes.getCode())){
                List<CustAuthDto> custAuthList = custAuthRes.getData();
                custAuthMap = custAuthList.stream().collect(Collectors.toMap(CustAuthDto::getCustId, custAuthDto -> custAuthDto));
            }
        }
        if(custAuthMap.containsKey(deviceVO.getCustId())){
            deviceVO.setUserName(custAuthMap.get(deviceVO.getCustId()).getUserName());
        }


        return deviceVO;
    }

    /**
     * 查询iot设备表列表
     *
     * @param deviceVO iot设备表
     * @return iot设备表
     */
    @Override
    public List<DeviceVO> selectDeviceList(DeviceVO deviceVO) {
        //左连接查询
        MPJLambdaWrapper<DeviceEntity> wrapper = getWrapper(deviceVO);
        List<DeviceEntity> deviceEntities = deviceMapper.selectJoinList(DeviceEntity.class,wrapper);

        List<Object> deviceList = deviceEntities.stream().map(DeviceEntity::getDeviceName).collect(Collectors.toList());
        List<String> custIdList = deviceEntities.stream().map(DeviceEntity::getCustId).distinct().collect(Collectors.toList());
        List<Object> produtList = deviceEntities.stream().map(DeviceEntity::getProductKey).distinct().collect(Collectors.toList());
        Map<String,CustAuthDto> custAuthMap = new HashMap<>();
        if(!custIdList.isEmpty()){
            R<List<CustAuthDto>> custAuthRes = remoteCustFeignService.getCustAuthByIds(custIdList.toArray(new String[]{}), SecurityConstants.INNER);
            if("200".equals(custAuthRes.getCode())){
                List<CustAuthDto> custAuthList = custAuthRes.getData();
                custAuthMap = custAuthList.stream().collect(Collectors.toMap(CustAuthDto::getCustId, custAuthDto -> custAuthDto));
            }

        }


        Map<String,JSONObject> statMap =  new HashMap<>();

        for(Object productKey : produtList){
            List<JSONObject> multiCacheMapValue = redisService.getMultiCacheMapValue(preRedisKey + productKey, deviceList);
                Map<String, JSONObject> tmpMap = multiCacheMapValue.stream().filter(item-> ObjUtil.isNotEmpty(item)).collect(Collectors.toMap(item -> item.getString("devId"), item -> item));
                statMap.putAll(tmpMap);
        }
//        deviceEntities = deviceEntities.stream().map(item -> {
//            if(statMap.containsKey(item.getDeviceName())){
//                BeanUtil.copyProperties(statMap.get(item.getDeviceName()), item);
//            }
//            return item;
//        }).collect(Collectors.toList());
        List<DeviceVO> list = PageObjectConvertUtil.convert(deviceEntities, DeviceVO.class);
        for(DeviceVO item : list){
            if(custAuthMap.containsKey(item.getCustId())){
                item.setUserName(custAuthMap.get(item.getCustId()).getUserName());
            }
            if(statMap.containsKey(item.getDeviceName())){
                int index = list.indexOf(item);
                BeanUtil.copyProperties(statMap.get(item.getDeviceName()), item);
                list.set(index,item);
            }

        }

        return list;
    }


    /**
     * 构造查询器
     */
    private MPJLambdaWrapper<DeviceEntity> getWrapper(DeviceVO query) {
        MPJLambdaWrapper<DeviceEntity> wrapper = new MPJLambdaWrapper<DeviceEntity>()
                .selectAll(DeviceEntity.class) // 查询DeviceEntity类的所有字段
                .selectAs(ProductEntity::getName, DeviceEntity::getProductName)// 查询ProductEntity类的name字段
                .leftJoin(ProductEntity.class, ProductEntity::getId, DeviceEntity::getProductId); // 左连接
//        wrapper.eq(ObjUtil.isNotEmpty(query.getDeviceKey()), DeviceEntity::getDeviceKey, query.getDeviceKey());

//        LambdaQueryWrapper<DeviceEntity> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ObjUtil.isNotEmpty(query.getDeviceKey()), DeviceEntity::getDeviceKey, query.getDeviceKey());
        wrapper.eq(ObjUtil.isNotEmpty(query.getId()), DeviceEntity::getId, query.getId());

        wrapper.like(ObjUtil.isNotEmpty(query.getSearchValue()), DeviceEntity::getDeviceName, query.getSearchValue())
                .or()
                .like(ObjUtil.isNotEmpty(query.getSearchValue()), DeviceEntity::getNickname, query.getSearchValue());

        wrapper.like(ObjUtil.isNotEmpty(query.getDeviceName()), DeviceEntity::getDeviceName, query.getDeviceName());
        wrapper.like(ObjUtil.isNotEmpty(query.getNickname()), DeviceEntity::getNickname, query.getNickname());
        wrapper.eq(ObjUtil.isNotEmpty(query.getSerialNumber()), DeviceEntity::getSerialNumber, query.getSerialNumber());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProductId()), DeviceEntity::getProductId, query.getProductId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getProductKey()), DeviceEntity::getProductKey, query.getProductKey());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDeviceType()), DeviceEntity::getDeviceType, query.getDeviceType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getStatus()), DeviceEntity::getStatus, query.getStatus());
        wrapper.eq(ObjUtil.isNotEmpty(query.getGatewayId()), DeviceEntity::getGatewayId, query.getGatewayId());
        wrapper.between(ArrayUtil.isAllNotEmpty(query.getBeginStatusLastUpdateTime(), query.getEndStatusLastUpdateTime()), DeviceEntity::getStatusLastUpdateTime, query.getBeginStatusLastUpdateTime(), query.getEndStatusLastUpdateTime());
        wrapper.between(ArrayUtil.isAllNotEmpty(query.getBeginLastOnlineTime(), query.getEndLastOnlineTime()), DeviceEntity::getLastOnlineTime, query.getBeginLastOnlineTime(), query.getEndLastOnlineTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getLastOfflineTime()), DeviceEntity::getLastOfflineTime, query.getLastOfflineTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getActiveTime()), DeviceEntity::getActiveTime, query.getActiveTime());
        wrapper.eq(ObjUtil.isNotEmpty(query.getIp()), DeviceEntity::getIp, query.getIp());
        wrapper.eq(ObjUtil.isNotEmpty(query.getFirmwareVersion()), DeviceEntity::getFirmwareVersion, query.getFirmwareVersion());
        wrapper.eq(ObjUtil.isNotEmpty(query.getDeviceSecret()), DeviceEntity::getDeviceSecret, query.getDeviceSecret());
        wrapper.eq(ObjUtil.isNotEmpty(query.getMqttClientId()), DeviceEntity::getMqttClientId, query.getMqttClientId());
        wrapper.like(ObjUtil.isNotEmpty(query.getMqttUsername()), DeviceEntity::getMqttUsername, query.getMqttUsername());
        wrapper.eq(ObjUtil.isNotEmpty(query.getMqttPassword()), DeviceEntity::getMqttPassword, query.getMqttPassword());
        wrapper.eq(ObjUtil.isNotEmpty(query.getAuthType()), DeviceEntity::getAuthType, query.getAuthType());
        wrapper.eq(ObjUtil.isNotEmpty(query.getLatitude()), DeviceEntity::getLatitude, query.getLatitude());
        wrapper.eq(ObjUtil.isNotEmpty(query.getLongitude()), DeviceEntity::getLongitude, query.getLongitude());
        wrapper.eq(ObjUtil.isNotEmpty(query.getAreaId()), DeviceEntity::getAreaId, query.getAreaId());
        wrapper.eq(ObjUtil.isNotEmpty(query.getAddress()), DeviceEntity::getAddress, query.getAddress());
//        wrapper.eq(ObjUtil.isNotEmpty(query.getCustId()), DeviceEntity::getCustId, query.getCustId());
        //查询名下设备列表
        if(StringUtils.isNotBlank(query.getCustId())){
            List<String> custIdList = commonCustService.selectSubCustIdsByCustId(query.getCustId());
            wrapper.in(!custIdList.isEmpty(),DeviceEntity::getCustId, custIdList);
        }
//        wrapper.orderByDesc(DeviceEntity::getDeviceSort,DeviceEntity::getCreateTime);
        wrapper.orderByAsc(DeviceEntity::getNickname);
        return wrapper;
    }

    /**
     * 新增iot设备表
     *
     * @param deviceVO iot设备表
     * @return 结果
     */
    @Override
    public int insertDevice(DeviceVO deviceVO) {
        DeviceEntity entity = BeanUtil.toBean(deviceVO, DeviceEntity.class);
        ProductVO productVO = productServiceImpl.selectProductById(entity.getProductId());
        entity.setProductKey(productVO.getProductKey());
        entity.setDeviceType(productVO.getDeviceType());
        entity.setDeviceKey(IdUtil.simpleUUID());
        entity.setStatus("0"); //设备状态:0-未激活;1-在线;2-离线;3-已禁用;
        DeviceEntity deviceByDevId = getDeviceByDevId(deviceVO.getDeviceName());
        if(deviceByDevId !=null && deviceByDevId.getId()>0){
            throw new BusinessException("设备id已存在");
        }

        return deviceMapper.insert(entity);
    }

    /**
     * 修改iot设备表
     *
     * @param deviceVO iot设备表
     * @return 结果
     */
    @Override
    public int updateDevice(DeviceVO deviceVO) {
        DeviceEntity entity = BeanUtil.toBean(deviceVO, DeviceEntity.class);
        return deviceMapper.updateById(entity);
    }

    /**
     * 批量删除iot设备表
     *
     * @param ids 需要删除的iot设备表主键
     * @return 结果
     */
    @Override
    public int deleteDeviceByIds(Long[] ids) {
        List<Long> idList = Arrays.stream(ids).collect(Collectors.toList());
        List<DeviceEntity> deviceEntities = deviceMapper.selectByIds(idList);
        deviceEntities.stream().forEach(item->{
            redisService.deleteCacheMapValue(preRedisKey+item.getProductKey(),item.getDeviceName());
        });

        return deviceMapper.deleteByIds(idList);
    }

    /**
     * 单笔删除iot设备表信息
     *
     * @param id iot设备表主键
     * @return 结果
     */
    @Override
    public int deleteDeviceById(Long id) {

        DeviceEntity item = deviceMapper.selectById(id);
        redisService.deleteCacheMapValue(preRedisKey+item.getProductKey(),item.getDeviceName());
        return deviceMapper.deleteById(id);
    }

    /**
     * 设备状态更新
     *
     * @param proId 产品ID
     * @param devId 设备id
     * @return 结果
     */
    @Async
    @Override
    public void deviceUpdateStatus(String proId, String devId, JSONObject msg) {
        String cmd = msg.getString("cmd");
        Date now = DateUtils.getNowDate();
        DeviceEntity deviceEntity = getDeviceByDevId(devId);
        if (deviceEntity != null) {
            DeviceEntity update = new DeviceEntity();
            update.setId(deviceEntity.getId());

            if (StrUtil.equals("0", deviceEntity.getStatus())) { //待激活
                update.setStatus("1"); //在线
                update.setActiveTime(now);
                update.setLastOnlineTime(now);
                update.setUpdateTime(now);
                update.setStatusLastUpdateTime(now);
                update.setSerialNumber(devId);
                deviceMapper.updateById(update);

                deviceEventServiceImpl.consumerDeviceEvent(deviceEntity, now, "1", "设备注册");
            }else{
                if (StrUtil.equals("1", deviceEntity.getStatus())) { //在线
                    update.setStatusLastUpdateTime(now);
                }

                if (StrUtil.equals("2", deviceEntity.getStatus())) { //离线
                    update.setStatus("1"); //在线
                    update.setStatusLastUpdateTime(now);
                    update.setLastOnlineTime(now);
                }
                deviceMapper.updateById(update);

                if("uploadStatus".equals(cmd)){ //状态上报

                    // 事件类型列表:0-设备状态上报;1-注册;2-离线;3-上线;4-缺水报警;
                    JSONObject jsonMsg =  JSONObject.parseObject(msg.toString());
                    String waterLevel = jsonMsg.getString("waterLevel");
                    if(waterLevel !=null && StrUtil.equals("0",waterLevel)){ //设备-缺水报警
                        deviceEventServiceImpl.consumerDeviceEvent(deviceEntity, now, "4", msg.toString());
                    }
                    deviceEventServiceImpl.consumerDeviceEvent(deviceEntity, now, "0", msg.toString());

                }
                if("reboot".equals(cmd)){ //设备重启-下发设置

                    log.info("设备下发设置:{}", deviceEntity.getDeviceName());
                    pushCmdSetting(deviceEntity);

                }
            }
        } else {
            log.info("设备未注册：devId=[{}]", devId);
        }

    }

    private DeviceEntity getDeviceByDevId(String devId) {
        DeviceEntity deviceEntity = deviceMapper.selectOne(Wrappers.<DeviceEntity>lambdaQuery().eq(DeviceEntity::getDeviceName, devId));
        return deviceEntity;
    }

    /**
     * mqtt 服务端事件处理
     *
     * @param topic     主题
     * @param eventJson 事件内容
     * @return 结果
     */
    @Override
    public void dealMqttEvent(String topic, JSONObject eventJson) {

        log.info("topic is :{} \n event is {}", topic, eventJson);

        String eventType = eventJson.getString("event");

        String clientid = eventJson.getString("clientid");
        String username = eventJson.getString("username");
        Date now = DateUtils.getNowDate();

        if (StrUtil.equals(IotConst.MqttEventType.CONNECTED.getCode(), eventType)) { //建立连接

            String devId = clientid.split("_")[1];
            DeviceEntity deviceEntity = getDeviceByDevId(devId);
            DeviceEntity update = new DeviceEntity();
            if (deviceEntity != null && StrUtil.equals("2", deviceEntity.getStatus())) { //设备离线更新状态
                update.setId(deviceEntity.getId());
                update.setStatus("1"); //设备状态:0-未激活;1-在线;2-离线;3-已禁用;
                update.setLastOnlineTime(now);
                update.setMqttClientId(clientid);
                update.setMqttUsername(username);
                deviceMapper.updateById(update);

                deviceEventServiceImpl.consumerDeviceEvent(deviceEntity, now, "3", eventJson.toString());
            }

//            if(deviceEntity != null ){ //下发命令-设置更新状态
//                log.info("设备下发设置:{}", deviceEntity.getDeviceName());
//                pushCmdSetting(deviceEntity);
//
//            }


        }
        if (StrUtil.equals(IotConst.MqttEventType.DISCONNECTED.getCode(), eventType)) { //断开连接

            String devId = clientid.split("_")[1];
            DeviceEntity deviceEntity = getDeviceByDevId(devId);
            DeviceEntity update = new DeviceEntity();
            if (deviceEntity != null && StrUtil.equals("1", deviceEntity.getStatus())) {
                update.setId(deviceEntity.getId());
                update.setStatus("2"); //设备状态:0-未激活;1-在线;2-离线;3-已禁用;
                update.setLastOfflineTime(now);
                deviceMapper.updateById(update);

                deviceEventServiceImpl.consumerDeviceEvent(deviceEntity, now, "2", eventJson.toString());
            }


        }


    }

    /**
     * 下发指令到设备
     *
     * @param deviceCmdVo 命令参数
     * @return 结果
     */
    @Override
    public JSONObject deviceCmd(DeviceCmdVo deviceCmdVo) {

        JSONObject res = new JSONObject();

        String devId = deviceCmdVo.getDevId();
        String cmd = deviceCmdVo.getCmd();
        DeviceEntity deviceEntity = getDeviceByDevId(devId);
        if (deviceEntity == null) {
            throw new BusinessException("设备不存在!");
        }

        DeviceTaskConfig deviceTaskConfig = null;
        String productKey = deviceEntity.getProductKey();
        if(StringUtils.equals(cmd,"startTime")){
             deviceTaskConfig = BeanUtil.toBean(deviceCmdVo, DeviceTaskConfig.class);
            DeviceEntity updateDeviceEntity = new DeviceEntity();
            updateDeviceEntity.setId(deviceEntity.getId());
            updateDeviceEntity.setTaskConfigList(ListUtil.of(deviceTaskConfig));

            deviceMapper.updateById(updateDeviceEntity);
        }
        if(StringUtils.equals(cmd,"stopTime")){
             deviceTaskConfig = null;
            if(deviceEntity.getTaskConfigList()!=null && deviceEntity.getTaskConfigList().size()>0){
                deviceTaskConfig = BeanUtil.toBean(deviceEntity.getTaskConfigList().get(0),DeviceTaskConfig.class);
            }else {
                deviceTaskConfig = BeanUtil.toBean(deviceCmdVo, DeviceTaskConfig.class);
            }
            deviceTaskConfig.setIsOpen("0");
            DeviceEntity updateDeviceEntity = new DeviceEntity();
            updateDeviceEntity.setId(deviceEntity.getId());
            updateDeviceEntity.setTaskConfigList(ListUtil.of(deviceTaskConfig));

            deviceMapper.updateById(updateDeviceEntity);

        }


        JSONObject req = new JSONObject();
        req.put("cmd", deviceCmdVo.getCmd());
        req.put("devId", devId);

        String topic = String.format("/qingma/%s/%s", productKey, devId);

        if(StringUtils.equals(cmd,"startTime")){

            //{"cmd":"startTime","devId":"898604F4152391195524","on":"60|30","off":"60|30","startHour":"08|10","startMin":"35|40","endHour":"10|20","endMin":"38|40","weekdays":"1000011|1000011"}
            String timeUnit = deviceTaskConfig.getTimeUnit();
            if("1".equals(timeUnit)){ //秒
                req.put("on", deviceTaskConfig.getOn());
                req.put("off", deviceTaskConfig.getOff());
            }else{
                req.put("on", NumberUtil.mul(deviceTaskConfig.getOn(),"60"));
                req.put("off",  NumberUtil.mul(deviceTaskConfig.getOff(),"60"));

            }
            req.put("weekdays",deviceTaskConfig.getWorkDay());
            req.put("startHour", deviceTaskConfig.getWorkTimeStart().split(":")[0]);
            req.put("startMin", deviceTaskConfig.getWorkTimeStart().split(":")[1]);
            req.put("endHour", deviceTaskConfig.getWorkTimeEnd().split(":")[0]);
            req.put("endMin", deviceTaskConfig.getWorkTimeEnd().split(":")[1]);
        }


        emqxClient.publish(topic, req.toString());





        if ("1".equals(deviceCmdVo.getSyncFlag())) {
            String key = devId + "_" + cmd;

            boolean returnFlag = true;
            long startTime = System.currentTimeMillis();
            long timeOut = 10000;

            while (returnFlag) {
                long current = System.currentTimeMillis();
                if (current - startTime > timeOut) {
                    returnFlag = false;
                }
                try {
                    Thread.sleep(RandomUtil.randomInt(50, 150));
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                Object object = cmdProcessImpl.getMap(key);
                if (object != null) {
                    res = JSONObject.parseObject(object.toString());
                    returnFlag = false;
                }
            }
            if("queryRunStatus".equals(cmd)){
                JSONObject cacheMapValue = redisService.getCacheMapValue("device_state:" + deviceEntity.getProductKey(), devId);
                cacheMapValue.put("cmd",cmd);
                return cacheMapValue;

            }else{
                return res;
            }

        } else {
            res.put("devId", devId);
            res.put("cmd", cmd);
            return res;
        }
    }

    /**
     * 查询emqx客户端列表
     *
     * @param qmqxClientReqVo
     */
    @Override
    public EmqxClientRespVo qryEmqxClientList(EmqxClientReqVo qmqxClientReqVo) {



        EmqxClientRespVo emqxClientRespVo = new EmqxClientRespVo();
        List<EmqxClientRespVo.ClientItem> allClientItem = getAllClientItem(qmqxClientReqVo);
        emqxClientRespVo.setTotal(allClientItem.size());
        emqxClientRespVo.setPageNum(qmqxClientReqVo.getPageNum());
        emqxClientRespVo.setPageSize(qmqxClientReqVo.getPageSize());

        List<EmqxClientRespVo.ClientItem> list = CollUtil.sortPageAll(
                qmqxClientReqVo.getPageNum()-1,
                qmqxClientReqVo.getPageSize(),
                new Comparator<EmqxClientRespVo.ClientItem>() {
                     @Override
                    public int compare(EmqxClientRespVo.ClientItem o1, EmqxClientRespVo.ClientItem o2) {
                        Date connectedAt1 = DateUtils.dateTime("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", o1.getConnectedAt());
                        Date connectedAt2 = DateUtils.dateTime("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", o2.getConnectedAt());

                        return DateUtils.truncatedCompareTo(connectedAt2,connectedAt1 , Calendar.SECOND);
                    }
        },allClientItem);

        List<String> clientIdList = list.stream().filter(item->item.getClientid().split("_").length > 1).map(item -> item.getClientid().split("_")[1]).collect(Collectors.toList());

        List<DeviceEntity> deviceEntities = deviceMapper.selectList(DeviceEntity::getDeviceName, clientIdList);
        Map<String, String> collectMap = deviceEntities.stream().collect(Collectors.toMap(DeviceEntity::getDeviceName, DeviceEntity::getDeviceName));

        list.stream().forEach(item->{
            item.setIsBind(false);
            if(item.getClientid().split("_").length > 1){
                if(collectMap.containsKey(item.getClientid().split("_")[1])){
                    item.setIsBind(true);
                }
            }
            item.setConnectedAt(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,DateUtils.dateTime("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", item.getConnectedAt())));
            item.setCreatedAt(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,DateUtils.dateTime("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", item.getCreatedAt())));
        });

        emqxClientRespVo.setList(list);

        return emqxClientRespVo;
    }

    /**
     * 获取所有客户端项目信息
     *
     * 此方法用于从EMQ X Broker获取所有客户端的详细信息它根据提供的查询条件来检索数据
     * 主要用于需要全面了解当前所有客户端状态的场景，以便进行监控、分析或展示
     *
     * @param qmqxClientReqVo 查询请求对象，包含查询客户端所需的各种参数
     * @return 返回一个列表，包含所有匹配查询条件的客户端项目信息如果查询到的客户端数量为零，将返回一个空列表
     */
    private List<EmqxClientRespVo.ClientItem> getAllClientItem(EmqxClientReqVo qmqxClientReqVo){
        if (StrUtil.isBlank(apiKey) || StrUtil.isBlank(apiSecret) || StrUtil.isBlank(apiUrl)) {
            throw new BusinessException("未配置emqx的apikey和apisecret");
        }

        String baseUrl = apiUrl + "/clients";
        JSONObject reqParam = JSONObject.from(qmqxClientReqVo);

        int currentPage = 1;
        int limit = 100; // 根据实际情况设置每页大小
        List<EmqxClientRespVo.ClientItem> allItems = new ArrayList<>();

        while (true) {
            // 设置分页参数
            reqParam.put("page", currentPage);
            reqParam.put("limit", limit);

            String resp = HttpUtil.createGet(baseUrl)
                    .basicAuth(apiKey, apiSecret)
                    .form(reqParam)
                    .execute()
                    .body();

            JSONObject respJson = JSONObject.parseObject(resp);
            JSONArray dataList = respJson.getJSONArray("data");

            if (dataList == null || dataList.isEmpty()) {
                break; // 没有更多数据
            }

            List<EmqxClientRespVo.ClientItem> items = JSONArray.parseArray(dataList.toJSONString(), EmqxClientRespVo.ClientItem.class);
            allItems.addAll(items);

            if (dataList.size() < limit) {
                break; // 当前页不足一页，说明已到最后一页
            }
            currentPage++;
        }
        return allItems;
    }

    /**
     * 下发设备命令配置
     */
    private void pushCmdSetting(DeviceEntity deviceEntity){
        List<DeviceTaskConfig> taskConfigList = deviceEntity.getTaskConfigList();
        if(taskConfigList != null && taskConfigList.size() > 0){
            DeviceTaskConfig deviceTaskConfig = BeanUtil.toBean(deviceEntity.getTaskConfigList().get(0),DeviceTaskConfig.class);
            DeviceCmdVo cmdVo = BeanUtil.toBean(deviceTaskConfig, DeviceCmdVo.class);
            cmdVo.setDevId(deviceEntity.getDeviceName());
            cmdVo.setCmd("startTime");
            deviceCmd(cmdVo);
        }
    }



}
