-- ------------------------------------
--  表名称:iot产品表
--  适用数据库：MySql
--  表名称：iot_product
--  字段前缀 ：无
--  最后修改人：<EMAIL>
--  最后修改日期：2025.01.07
-- ------------------------------------

DROP TABLE IF EXISTS iot_product;
CREATE TABLE iot_product (
  id                           bigint NOT NULL AUTO_INCREMENT    comment '产品ID',
  name                         varchar(32)   NOT NULL            comment '产品名称',
  product_key                  varchar(64)   DEFAULT NULL        comment '产品标识',
  category_id                  varchar(64)   DEFAULT NULL        comment '产品所属品类编号',
  description                  varchar(64)   DEFAULT NULL        comment '产品描述',
  status                       varchar(2)    DEFAULT NULL        comment '产品状态:0-开发中;1-已发布;',
  device_type                  varchar(2)    DEFAULT NULL        comment '设备类型:0-直连设备;1-网关子设备;2-网关设备;',
  net_type                     varchar(2)    DEFAULT NULL        comment '联网方式:0-WiFi;1-Cellular;2-Ethernet;3-其他;',
  protocol_type                varchar(2)    DEFAULT NULL        comment '接入网关协议:0-自定义;1-Modbus;2-OPC UA;3-ZigBee;4-BLE;',
  protocol_id                  bigint        DEFAULT NULL        comment '协议编号',
  data_format                  varchar(2)    DEFAULT NULL        comment '数据格式:0-标准数据格式（JSON）;1-透传/自定义;',
  validate_type                varchar(2)    DEFAULT NULL        comment '数据校验级别:0-弱校验;1-免校验',
  delete_flag                  varchar(2)    DEFAULT NULL        comment '是否删除：0-正常；1-删除；',
  create_time                  datetime      DEFAULT NULL        comment '创建时间',
  create_by                    varchar(64)   DEFAULT NULL        comment '创建用户id',
  update_time                  datetime      DEFAULT NULL        comment '修改时间',
  update_by                    varchar(64)   DEFAULT NULL        comment '修改用户id',
  tenant_id                    bigint        NOT NULL DEFAULT 0  comment '租户编号',
  primary key (id)
) comment 'iot产品表';
