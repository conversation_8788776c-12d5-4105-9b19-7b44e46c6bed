package com.tianmasys.ebu.qingma.service;

import java.util.List;
import com.tianmasys.ebu.qingma.vo.ThinkModelFunctionVO;
import com.tianmasys.ebu.qingma.domain.ThinkModelFunctionEntity;
import com.baomidou.mybatisplus.extension.service.IService;


/**
 * iot产品物模型功能Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-17
 */
public interface IThinkModelFunctionService extends IService<ThinkModelFunctionEntity>
{
    /**
     * 查询thinkModelFunction
     * 
     * @param id thinkModelFunction主键
     * @return thinkModelFunction
     */
    public ThinkModelFunctionVO selectThinkModelFunctionById(Long id);

    /**
     * 查询thinkModelFunction列表
     * 
     * @param thinkModelFunctionVO thinkModelFunction
     * @return thinkModelFunction集合
     */
    public List<ThinkModelFunctionVO> selectThinkModelFunctionList(ThinkModelFunctionVO thinkModelFunctionVO);

    /**
     * 新增thinkModelFunction
     * 
     * @param thinkModelFunctionVO thinkModelFunction
     * @return 结果
     */
    public int insertThinkModelFunction(ThinkModelFunctionVO thinkModelFunctionVO);

    /**
     * 修改thinkModelFunction
     * 
     * @param thinkModelFunctionVO thinkModelFunction
     * @return 结果
     */
    public int updateThinkModelFunction(ThinkModelFunctionVO thinkModelFunctionVO);

    /**
     * 批量删除thinkModelFunction
     * 
     * @param ids 需要删除的thinkModelFunction主键集合
     * @return 结果
     */
    public int deleteThinkModelFunctionByIds(Long[] ids);

    /**
     * 删除thinkModelFunction信息
     * 
     * @param id thinkModelFunction主键
     * @return 结果
     */
    public int deleteThinkModelFunctionById(Long id);
}
