package com.tianmasys.ebu.qingma.service;

import java.util.List;
import com.tianmasys.ebu.qingma.vo.CustRelationVO;
import com.tianmasys.ebu.qingma.domain.CustRelationEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tianmasys.ebu.qingma.vo.SubCustVo;


/**
 * 客户关联关系Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-02-21
 */
public interface ICustRelationService extends IService<CustRelationEntity>
{
    /**
     * 查询custRelation
     * 
     * @param custId custRelation主键
     * @return custRelation
     */
    public CustRelationVO selectCustRelationByCustId(String custId);

    /**
     * 查询custRelation列表
     * 
     * @param custRelationVO custRelation
     * @return custRelation集合
     */
    public List<CustRelationVO> selectCustRelationList(CustRelationVO custRelationVO);
    /**
     * 查询custRelation列表
     *
     * @param custRelationVO custRelation
     * @return custRelation集合
     */

    /**
     * 新增custRelation
     * 
     * @param custRelationVO custRelation
     * @return 结果
     */
    public int insertCustRelation(CustRelationVO custRelationVO);

    /**
     * 修改custRelation
     * 
     * @param custRelationVO custRelation
     * @return 结果
     */
    public int updateCustRelation(CustRelationVO custRelationVO);

    /**
     * 批量删除custRelation
     * 
     * @param custIds 需要删除的custRelation主键集合
     * @return 结果
     */
    public int deleteCustRelationByCustIds(String[] custIds);

    /**
     * 删除custRelation信息
     * 
     * @param custId custRelation主键
     * @return 结果
     */
    public int deleteCustRelationByCustId(String custId);

    /**
     * 添加子账号
     *
     * @param subCustVo 子账号内容
     * @return 结果
     */
    public int addSubCust(SubCustVo subCustVo);
}
