package com.tianmasys.ebu.qingma.service;

import java.util.Date;
import java.util.List;

import com.tianmasys.ebu.qingma.domain.DeviceEntity;
import com.tianmasys.ebu.qingma.vo.DeviceEventVO;
import com.tianmasys.ebu.qingma.domain.DeviceEventEntity;
import com.baomidou.mybatisplus.extension.service.IService;


/**
 * iot设备事件表Service接口
 * 
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-01-19
 */
public interface IDeviceEventService extends IService<DeviceEventEntity>
{
    /**
     * 查询deviceEvent
     * 
     * @param id deviceEvent主键
     * @return deviceEvent
     */
    public DeviceEventVO selectDeviceEventById(Long id);

    /**
     * 查询deviceEvent列表
     * 
     * @param deviceEventVO deviceEvent
     * @return deviceEvent集合
     */
    public List<DeviceEventVO> selectDeviceEventList(DeviceEventVO deviceEventVO);

    /**
     * 新增deviceEvent
     * 
     * @param deviceEventVO deviceEvent
     * @return 结果
     */
    public int insertDeviceEvent(DeviceEventVO deviceEventVO);

    /**
     * 修改deviceEvent
     * 
     * @param deviceEventVO deviceEvent
     * @return 结果
     */
    public int updateDeviceEvent(DeviceEventVO deviceEventVO);

    /**
     * 批量删除deviceEvent
     * 
     * @param ids 需要删除的deviceEvent主键集合
     * @return 结果
     */
    public int deleteDeviceEventByIds(Long[] ids);

    /**
     * 删除deviceEvent信息
     * 
     * @param id deviceEvent主键
     * @return 结果
     */
    public int deleteDeviceEventById(Long id);


    /**
     * 保存设备消息
     *
     * @param deviceEntity 设备实体
     * @param eventTime 事件时间
     * @param eventType 事件类型:0-设备状态上报;1-注册;2-离线;3-上线;4-缺水报警;
     * @param eventContent 事件内容
     * @return 结果
     */
    public void consumerDeviceEvent(DeviceEntity deviceEntity, Date eventTime, String eventType, String eventContent);
}
