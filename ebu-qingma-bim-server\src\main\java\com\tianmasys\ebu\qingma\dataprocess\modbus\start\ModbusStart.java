package com.tianmasys.ebu.qingma.dataprocess.modbus.start;

import com.tianmasys.ebu.qingma.dataprocess.modbus.client.ModbusClient;
import com.tianmasys.ebu.qingma.dataprocess.modbus.config.ModbusConfig;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 用于在应用启动时自动连接modbus服务
 *
 * <AUTHOR>
 */
@Component
public class ModbusStart implements ApplicationRunner {

//    @Resource
//    private ModbusClient modbusClient;

//    @Resource
//    private ModbusConfig modbusConfig;

    @Override
    public void run(ApplicationArguments applicationArguments) {
//       modbusConfig.initClient();
    }
}
