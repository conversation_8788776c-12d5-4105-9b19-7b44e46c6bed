package com.tianmasys.ebu.qingma.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tianmasys.ebu.common.log.annotation.Log;
import com.tianmasys.ebu.common.log.enums.BusinessType;
import com.tianmasys.ebu.common.security.annotation.RequiresPermissions;
import com.tianmasys.ebu.qingma.vo.DeviceEventVO;
import com.tianmasys.ebu.qingma.service.IDeviceEventService;
import com.tianmasys.ebu.common.core.web.controller.BaseController;
import com.tianmasys.ebu.common.core.web.domain.AjaxResult;
import com.tianmasys.ebu.common.core.web.page.TableDataInfo;
import com.tianmasys.ebu.common.core.utils.poi.ExcelUtil;
import javax.validation.Valid;

/**
 * iot设备事件表Controller
 * 
* @<NAME_EMAIL>
* @since 1.0.0 2025-01-19
 */
@RestController
@RequestMapping("/deviceEvent")
public class DeviceEventController extends BaseController
{
    @Autowired
    private IDeviceEventService deviceEventService;

    /**
     * 查询iot设备事件表列表
     */
    @RequiresPermissions("iot:deviceEvent:list")
    @GetMapping("/list")
    public AjaxResult<TableDataInfo<DeviceEventVO>> list(DeviceEventVO deviceEventVO)
    {
        startPage();
        List<DeviceEventVO> list = deviceEventService.selectDeviceEventList(deviceEventVO);
        return success(getDataTable(list));
    }


    /**
     * 导出iot设备事件表列表
     */
    @RequiresPermissions("iot:deviceEvent:export")
    @Log(title = "iot设备事件表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DeviceEventVO deviceEventVO)
    {
        List<DeviceEventVO> list = deviceEventService.selectDeviceEventList(deviceEventVO);
        ExcelUtil<DeviceEventVO> util = new ExcelUtil<DeviceEventVO>(DeviceEventVO.class);
        util.exportExcel(response, list, "iot设备事件表数据");
    }

    /**
     * 获取iot设备事件表详细信息
     */
    @RequiresPermissions("iot:deviceEvent:query")
    @GetMapping(value = "/{id}")
    public AjaxResult<DeviceEventVO> getInfo(@PathVariable("id") Long id)
    {
        return success(deviceEventService.selectDeviceEventById(id));
    }

    /**
     * 新增iot设备事件表
     */
    @RequiresPermissions("iot:deviceEvent:add")
    @Log(title = "iot设备事件表", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public AjaxResult<DeviceEventVO> add(@Valid @RequestBody DeviceEventVO deviceEventVO)
    {
        return toAjax(deviceEventService.insertDeviceEvent(deviceEventVO));
    }

    /**
     * 修改iot设备事件表
     */
    @RequiresPermissions("iot:deviceEvent:edit")
    @Log(title = "iot设备事件表", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult<DeviceEventVO> edit(@Valid @RequestBody DeviceEventVO deviceEventVO)
    {
        return toAjax(deviceEventService.updateDeviceEvent(deviceEventVO));
    }

    /**
     * 删除iot设备事件表
     */
    @RequiresPermissions("iot:deviceEvent:remove")
    @Log(title = "iot设备事件表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult<DeviceEventVO> remove(@PathVariable Long[] ids)
    {
        return toAjax(deviceEventService.deleteDeviceEventByIds(ids));
    }
}
